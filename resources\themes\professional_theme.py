"""
Professional theme configuration for the Management System
"""

# Professional Color Palette
PROFESSIONAL_COLORS = {
    # Primary Colors
    'primary': '#2E86AB',          # Professional Blue
    'primary_dark': '#1e5f7a',     # Darker Blue for hover
    'primary_light': '#4a9bc7',    # <PERSON>er <PERSON> for accents
    
    # Secondary Colors
    'secondary': '#A23B72',        # Accent Purple
    'secondary_dark': '#7a2d5a',   # Dark<PERSON> Purple
    'secondary_light': '#b85a8a',  # Lighter Purple
    
    # Status Colors
    'success': '#28A745',          # <PERSON> Green
    'success_dark': '#1e7e34',     # Darker Green
    'warning': '#FFC107',          # Warning Yellow
    'warning_dark': '#d39e00',     # Darker Yellow
    'danger': '#DC3545',           # Danger Red
    'danger_dark': '#bd2130',      # Darker Red
    'info': '#17A2B8',            # Info Cyan
    'info_dark': '#117a8b',       # Darker Cyan
    
    # Neutral Colors
    'white': '#FFFFFF',            # Pure White
    'light': '#F8F9FA',           # <PERSON> Gray
    'light_gray': '#E9ECEF',      # Light Gray 2
    'gray': '#6C757D',            # <PERSON> Gray
    'dark_gray': '#495057',       # Dark Gray
    'dark': '#343A40',            # Very Dark Gray
    'black': '#000000',           # Pure Black
    
    # Background Colors
    'bg_primary': '#FFFFFF',       # Primary Background
    'bg_secondary': '#F8F9FA',     # Secondary Background
    'bg_sidebar': '#2C3E50',       # Sidebar Background
    'bg_card': '#FFFFFF',          # Card Background
    'bg_hover': '#F1F3F4',        # Hover Background
    
    # Border Colors
    'border_light': '#DEE2E6',     # Light Border
    'border_medium': '#CED4DA',    # Medium Border
    'border_dark': '#ADB5BD',      # Dark Border
    
    # Text Colors
    'text_primary': '#212529',     # Primary Text
    'text_secondary': '#6C757D',   # Secondary Text
    'text_muted': '#ADB5BD',      # Muted Text
    'text_white': '#FFFFFF',      # White Text
    
    # Gradient Colors
    'gradient_start': '#667eea',   # Gradient Start
    'gradient_end': '#764ba2',     # Gradient End
}

# Font Configuration
FONTS = {
    'primary': 'Segoe UI',         # Primary Font
    'secondary': 'Arial',          # Secondary Font
    'monospace': 'Consolas',       # Monospace Font
    'arabic': 'Tahoma',           # Arabic Font
    
    # Font Sizes
    'size_small': 10,
    'size_normal': 12,
    'size_medium': 14,
    'size_large': 16,
    'size_xlarge': 18,
    'size_xxlarge': 24,
    'size_title': 28,
    'size_hero': 32,
}

# Component Styling
COMPONENT_STYLES = {
    'button': {
        'height': 40,
        'corner_radius': 8,
        'border_width': 0,
        'font_size': FONTS['size_normal'],
        'font_weight': 'normal',
    },
    
    'button_primary': {
        'fg_color': PROFESSIONAL_COLORS['primary'],
        'hover_color': PROFESSIONAL_COLORS['primary_dark'],
        'text_color': PROFESSIONAL_COLORS['text_white'],
    },
    
    'button_secondary': {
        'fg_color': PROFESSIONAL_COLORS['secondary'],
        'hover_color': PROFESSIONAL_COLORS['secondary_dark'],
        'text_color': PROFESSIONAL_COLORS['text_white'],
    },
    
    'button_success': {
        'fg_color': PROFESSIONAL_COLORS['success'],
        'hover_color': PROFESSIONAL_COLORS['success_dark'],
        'text_color': PROFESSIONAL_COLORS['text_white'],
    },
    
    'button_danger': {
        'fg_color': PROFESSIONAL_COLORS['danger'],
        'hover_color': PROFESSIONAL_COLORS['danger_dark'],
        'text_color': PROFESSIONAL_COLORS['text_white'],
    },
    
    'entry': {
        'height': 40,
        'corner_radius': 8,
        'border_width': 2,
        'border_color': PROFESSIONAL_COLORS['border_light'],
        'fg_color': PROFESSIONAL_COLORS['white'],
        'text_color': PROFESSIONAL_COLORS['text_primary'],
    },
    
    'frame': {
        'corner_radius': 12,
        'border_width': 1,
        'border_color': PROFESSIONAL_COLORS['border_light'],
        'fg_color': PROFESSIONAL_COLORS['bg_card'],
    },
    
    'card': {
        'corner_radius': 15,
        'border_width': 1,
        'border_color': PROFESSIONAL_COLORS['border_light'],
        'fg_color': PROFESSIONAL_COLORS['bg_card'],
    },
    
    'sidebar': {
        'corner_radius': 0,
        'fg_color': PROFESSIONAL_COLORS['bg_sidebar'],
        'width': 280,
    },
    
    'header': {
        'height': 80,
        'corner_radius': 0,
        'fg_color': PROFESSIONAL_COLORS['bg_primary'],
        'border_width': 1,
        'border_color': PROFESSIONAL_COLORS['border_light'],
    },
}

# Animation Settings
ANIMATIONS = {
    'duration_fast': 150,      # Fast animations (ms)
    'duration_normal': 250,    # Normal animations (ms)
    'duration_slow': 400,      # Slow animations (ms)
    
    'easing': 'ease_out',      # Animation easing
}

# Layout Settings
LAYOUT = {
    'padding_small': 8,
    'padding_normal': 16,
    'padding_large': 24,
    'padding_xlarge': 32,
    
    'margin_small': 4,
    'margin_normal': 8,
    'margin_large': 16,
    'margin_xlarge': 24,
    
    'border_radius_small': 4,
    'border_radius_normal': 8,
    'border_radius_large': 12,
    'border_radius_xlarge': 16,
    
    'shadow_small': '0 1px 3px rgba(0,0,0,0.12)',
    'shadow_normal': '0 4px 6px rgba(0,0,0,0.1)',
    'shadow_large': '0 10px 25px rgba(0,0,0,0.15)',
}

# Icon Configuration
ICONS = {
    # Navigation Icons
    'dashboard': '🏠',
    'customers': '👥',
    'inventory': '📦',
    'sales': '💰',
    'purchases': '🛒',
    'suppliers': '🏪',
    'employees': '👨‍💼',
    'finance': '💳',
    'invoices': '🧾',
    'reports': '📊',
    'documents': '📁',
    'settings': '⚙️',
    'logout': '🚪',
    
    # Action Icons
    'add': '➕',
    'edit': '✏️',
    'delete': '🗑️',
    'view': '👁️',
    'search': '🔍',
    'filter': '🔽',
    'export': '📤',
    'import': '📥',
    'print': '🖨️',
    'save': '💾',
    'cancel': '❌',
    'confirm': '✅',
    
    # Status Icons
    'success': '✅',
    'error': '❌',
    'warning': '⚠️',
    'info': 'ℹ️',
    'loading': '⏳',
    
    # Business Icons
    'money': '💰',
    'chart': '📈',
    'calendar': '📅',
    'clock': '🕐',
    'location': '📍',
    'phone': '📞',
    'email': '📧',
    'website': '🌐',
    'user': '👤',
    'company': '🏢',
}

def get_color(color_name):
    """Get color by name"""
    return PROFESSIONAL_COLORS.get(color_name, '#000000')

def get_font_config(font_type='primary', size='normal', weight='normal'):
    """Get font configuration"""
    font_family = FONTS.get(font_type, FONTS['primary'])
    font_size = FONTS.get(f'size_{size}', FONTS['size_normal'])
    
    return {
        'family': font_family,
        'size': font_size,
        'weight': weight
    }

def get_component_style(component_type, variant='default'):
    """Get component styling"""
    base_style = COMPONENT_STYLES.get(component_type, {})
    variant_style = COMPONENT_STYLES.get(f'{component_type}_{variant}', {})
    
    # Merge styles
    return {**base_style, **variant_style}

def get_icon(icon_name):
    """Get icon by name"""
    return ICONS.get(icon_name, '❓')
