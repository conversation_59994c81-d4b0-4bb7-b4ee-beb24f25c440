"""
Login view for the Management System - Professional UI
"""
import customtkinter as ctk
from tkinter import messagebox
import logging
from PIL import Image, ImageTk
import os

logger = logging.getLogger(__name__)

# Set appearance mode and color theme
ctk.set_appearance_mode("light")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

class LoginView:
    """Professional login window for user authentication"""

    def __init__(self, app):
        self.app = app
        self.root = None
        self.username_var = None
        self.password_var = None
        self.language_var = None
        self.login_successful = False

        # UI Colors - Professional theme
        self.colors = {
            'primary': '#2E86AB',      # Professional Blue
            'secondary': '#A23B72',    # Accent Purple
            'success': '#28A745',      # Success Green
            'warning': '#FFC107',      # Warning Yellow
            'danger': '#DC3545',       # Danger Red
            'light': '#F8F9FA',        # Light Gray
            'dark': '#343A40',         # <PERSON> Gray
            'white': '#FFFFFF',        # Pure White
            'gradient_start': '#667eea',
            'gradient_end': '#764ba2'
        }
        
    def show_login(self):
        """Show login window and return True if login successful"""
        self.create_login_window()
        self.root.mainloop()
        return self.login_successful
    
    def create_login_window(self):
        """Create the professional login window"""
        self.root = ctk.CTk()
        self.root.title(self.app.language_service.translate("login.title"))
        self.root.geometry("500x700")
        self.root.resizable(False, False)

        # Set window icon (if available)
        try:
            self.root.iconbitmap("resources/icons/app_icon.ico")
        except:
            pass  # Icon file not found, continue without it

        # Center the window
        self.center_window()

        # Create main container with gradient-like effect
        self.create_main_container()
        
    def create_main_container(self):
        """Create the main container with professional styling"""
        # Main container
        main_frame = ctk.CTkFrame(
            self.root,
            corner_radius=0,
            fg_color="transparent"
        )
        main_frame.pack(fill="both", expand=True, padx=0, pady=0)

        # Header section with logo and title
        self.create_header_section(main_frame)

        # Login form section
        self.create_login_form(main_frame)

        # Footer section
        self.create_footer_section(main_frame)

    def create_header_section(self, parent):
        """Create the header section with logo and title"""
        header_frame = ctk.CTkFrame(
            parent,
            height=200,
            corner_radius=0,
            fg_color=self.colors['primary']
        )
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)

        # Logo placeholder (you can add an actual logo image here)
        logo_frame = ctk.CTkFrame(
            header_frame,
            width=80,
            height=80,
            corner_radius=40,
            fg_color=self.colors['white']
        )
        logo_frame.pack(pady=(40, 20))

        # Logo icon (using emoji for now, can be replaced with actual icon)
        logo_label = ctk.CTkLabel(
            logo_frame,
            text="🏢",
            font=ctk.CTkFont(size=40),
            text_color=self.colors['primary']
        )
        logo_label.pack(expand=True)

        # Title
        title_label = ctk.CTkLabel(
            header_frame,
            text=self.app.language_service.translate("app.title"),
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=self.colors['white']
        )
        title_label.pack(pady=(0, 10))

        # Subtitle
        subtitle_label = ctk.CTkLabel(
            header_frame,
            text=self.app.language_service.translate("login.welcome"),
            font=ctk.CTkFont(size=14),
            text_color=self.colors['light']
        )
        subtitle_label.pack()
        
    def create_login_form(self, parent):
        """Create the login form with professional styling"""
        form_frame = ctk.CTkFrame(
            parent,
            corner_radius=20,
            fg_color=self.colors['white'],
            border_width=1,
            border_color=self.colors['light']
        )
        form_frame.pack(fill="both", expand=True, padx=40, pady=30)

        # Form title
        form_title = ctk.CTkLabel(
            form_frame,
            text=self.app.language_service.translate("login.title"),
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.colors['dark']
        )
        form_title.pack(pady=(40, 30))

        # Language selection with modern styling
        lang_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        lang_frame.pack(fill="x", padx=40, pady=(0, 20))

        lang_label = ctk.CTkLabel(
            lang_frame,
            text=self.app.language_service.translate("login.language"),
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['dark']
        )
        lang_label.pack(anchor="w", pady=(0, 8))

        self.language_var = ctk.StringVar(value=self.app.language_service.current_language)
        language_combo = ctk.CTkComboBox(
            lang_frame,
            variable=self.language_var,
            values=["en", "ar"],
            state="readonly",
            width=120,
            height=35,
            font=ctk.CTkFont(size=12),
            dropdown_font=ctk.CTkFont(size=12),
            command=self.on_language_change
        )
        language_combo.pack(anchor="w")
        
        # Username field with modern styling
        username_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        username_frame.pack(fill="x", padx=40, pady=(20, 15))

        username_label = ctk.CTkLabel(
            username_frame,
            text=f"👤 {self.app.language_service.translate('login.username')}",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['dark']
        )
        username_label.pack(anchor="w", pady=(0, 8))

        self.username_var = ctk.StringVar(value="admin")  # Default for testing
        username_entry = ctk.CTkEntry(
            username_frame,
            textvariable=self.username_var,
            height=45,
            font=ctk.CTkFont(size=14),
            placeholder_text="Enter your username",
            border_width=2,
            corner_radius=10
        )
        username_entry.pack(fill="x")

        # Password field with modern styling
        password_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        password_frame.pack(fill="x", padx=40, pady=(15, 20))

        password_label = ctk.CTkLabel(
            password_frame,
            text=f"🔒 {self.app.language_service.translate('login.password')}",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['dark']
        )
        password_label.pack(anchor="w", pady=(0, 8))

        self.password_var = ctk.StringVar(value="admin123")  # Default for testing
        password_entry = ctk.CTkEntry(
            password_frame,
            textvariable=self.password_var,
            height=45,
            font=ctk.CTkFont(size=14),
            placeholder_text="Enter your password",
            show="*",
            border_width=2,
            corner_radius=10
        )
        password_entry.pack(fill="x")
        
        # Login button with gradient-like effect
        login_button = ctk.CTkButton(
            form_frame,
            text=f"🚀 {self.app.language_service.translate('login.login_button')}",
            command=self.on_login_click,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            corner_radius=25,
            fg_color=self.colors['primary'],
            hover_color=self.colors['secondary']
        )
        login_button.pack(fill="x", padx=40, pady=(30, 20))

        # Forgot password link with modern styling
        forgot_button = ctk.CTkButton(
            form_frame,
            text=self.app.language_service.translate("login.forgot_password"),
            command=self.on_forgot_password,
            height=35,
            font=ctk.CTkFont(size=12),
            fg_color="transparent",
            text_color=self.colors['primary'],
            hover_color=self.colors['light'],
            corner_radius=10
        )
        forgot_button.pack(pady=(0, 40))

        # Bind Enter key to login
        self.root.bind('<Return>', lambda e: self.on_login_click())

        # Focus on username field
        username_entry.focus()

        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)

    def create_footer_section(self, parent):
        """Create the footer section"""
        footer_frame = ctk.CTkFrame(
            parent,
            height=60,
            corner_radius=0,
            fg_color=self.colors['light']
        )
        footer_frame.pack(fill="x", side="bottom")
        footer_frame.pack_propagate(False)

        # Copyright text
        copyright_label = ctk.CTkLabel(
            footer_frame,
            text="© 2024 Management System - Professional Business Solution",
            font=ctk.CTkFont(size=11),
            text_color=self.colors['dark']
        )
        copyright_label.pack(expand=True)
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = 500
        height = 700
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def on_language_change(self, choice=None):
        """Handle language change"""
        new_language = self.language_var.get()
        if self.app.language_service.set_language(new_language):
            # Update the window title and labels without recreating the window
            self.update_language_texts()

    def update_language_texts(self):
        """Update all text elements with new language"""
        try:
            # Update window title
            self.root.title(self.app.language_service.translate("login.title"))

            # Store references to widgets that need updating
            # For now, show a simple message that language was changed
            self.show_language_change_message()

        except Exception as e:
            logger.error(f"Error updating language texts: {e}")

    def show_language_change_message(self):
        """Show message that language was changed"""
        # Create a simple info dialog
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Language Changed")
        dialog.geometry("300x150")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 150
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 75
        dialog.geometry(f"300x150+{x}+{y}")

        # Content
        icon_label = ctk.CTkLabel(
            dialog,
            text="🌐",
            font=ctk.CTkFont(size=30)
        )
        icon_label.pack(pady=(20, 10))

        message_text = "Language changed successfully!" if self.app.language_service.current_language == 'en' else "تم تغيير اللغة بنجاح!"

        message_label = ctk.CTkLabel(
            dialog,
            text=message_text,
            font=ctk.CTkFont(size=12),
            wraplength=250
        )
        message_label.pack(pady=(0, 15))

        ok_button = ctk.CTkButton(
            dialog,
            text="OK" if self.app.language_service.current_language == 'en' else "موافق",
            command=dialog.destroy,
            width=80,
            height=30,
            fg_color=self.colors['primary']
        )
        ok_button.pack(pady=(0, 15))

        # Auto close after 2 seconds
        dialog.after(2000, dialog.destroy)
    
    def on_login_click(self):
        """Handle login button click"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        if not username or not password:
            self.show_error_message(
                self.app.language_service.translate("common.error"),
                "Please enter both username and password"
            )
            return
        
        try:
            # Attempt authentication
            if self.authenticate_user(username, password):
                self.login_successful = True
                logger.info(f"User {username} logged in successfully")
                self.show_success_message(
                    self.app.language_service.translate("common.success"),
                    self.app.language_service.translate("login.login_success")
                )
                self.root.destroy()
            else:
                self.show_error_message(
                    self.app.language_service.translate("common.error"),
                    self.app.language_service.translate("login.invalid_credentials")
                )
                
        except Exception as e:
            logger.error(f"Login error: {e}")
            self.show_error_message(
                self.app.language_service.translate("common.error"),
                f"Login failed: {str(e)}"
            )
    
    def authenticate_user(self, username, password):
        """Authenticate user credentials"""
        try:
            # Simple authentication for now
            # In production, this should use proper password hashing
            if username == "admin" and password == "admin123":
                # Create a simple user object
                class User:
                    def __init__(self, username, email, first_name, last_name):
                        self.username = username
                        self.email = email
                        self.first_name = first_name
                        self.last_name = last_name
                
                self.app.current_user = User("admin", "<EMAIL>", "System", "Administrator")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def on_forgot_password(self, event=None):
        """Handle forgot password click"""
        messagebox.showinfo(
            "Forgot Password",
            "Please contact your system administrator to reset your password."
        )
    
    def show_success_message(self, title, message):
        """Show success message with custom styling"""
        # Create custom success dialog
        dialog = ctk.CTkToplevel(self.root)
        dialog.title(title)
        dialog.geometry("350x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 175
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 100
        dialog.geometry(f"350x200+{x}+{y}")

        # Success icon and message
        icon_label = ctk.CTkLabel(
            dialog,
            text="✅",
            font=ctk.CTkFont(size=40)
        )
        icon_label.pack(pady=(30, 10))

        message_label = ctk.CTkLabel(
            dialog,
            text=message,
            font=ctk.CTkFont(size=14),
            wraplength=300
        )
        message_label.pack(pady=(0, 20))

        ok_button = ctk.CTkButton(
            dialog,
            text="OK",
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color=self.colors['success']
        )
        ok_button.pack(pady=(0, 20))

        # Auto close after 2 seconds
        dialog.after(2000, dialog.destroy)

    def show_error_message(self, title, message):
        """Show error message with custom styling"""
        # Create custom error dialog
        dialog = ctk.CTkToplevel(self.root)
        dialog.title(title)
        dialog.geometry("350x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 175
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 100
        dialog.geometry(f"350x200+{x}+{y}")

        # Error icon and message
        icon_label = ctk.CTkLabel(
            dialog,
            text="❌",
            font=ctk.CTkFont(size=40)
        )
        icon_label.pack(pady=(30, 10))

        message_label = ctk.CTkLabel(
            dialog,
            text=message,
            font=ctk.CTkFont(size=14),
            wraplength=300
        )
        message_label.pack(pady=(0, 20))

        ok_button = ctk.CTkButton(
            dialog,
            text="OK",
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color=self.colors['danger']
        )
        ok_button.pack(pady=(0, 20))

    def on_window_close(self):
        """Handle window close event"""
        self.login_successful = False
        self.root.destroy()
