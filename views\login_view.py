"""
Login view for the Management System
"""
import tkinter as tk
from tkinter import ttk, messagebox
import logging

logger = logging.getLogger(__name__)

class LoginView:
    """Login window for user authentication"""
    
    def __init__(self, app):
        self.app = app
        self.root = None
        self.username_var = None
        self.password_var = None
        self.language_var = None
        self.login_successful = False
        
    def show_login(self):
        """Show login window and return True if login successful"""
        self.create_login_window()
        self.root.mainloop()
        return self.login_successful
    
    def create_login_window(self):
        """Create the login window"""
        self.root = tk.Tk()
        self.root.title(self.app.language_service.translate("login.title"))
        self.root.geometry("400x500")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Configure style
        self.configure_style()
        
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Logo/Title section
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 30))
        
        title_label = ttk.Label(
            title_frame,
            text=self.app.language_service.translate("app.title"),
            font=("Segoe UI", 18, "bold"),
            anchor="center"
        )
        title_label.pack()
        
        version_label = ttk.Label(
            title_frame,
            text=self.app.language_service.translate("app.version"),
            font=("Segoe UI", 10),
            anchor="center"
        )
        version_label.pack(pady=(5, 0))
        
        # Language selection
        lang_frame = ttk.Frame(main_frame)
        lang_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(lang_frame, text=self.app.language_service.translate("login.language")).pack(anchor="w")
        
        self.language_var = tk.StringVar(value=self.app.language_service.current_language)
        language_combo = ttk.Combobox(
            lang_frame,
            textvariable=self.language_var,
            values=["en", "ar"],
            state="readonly",
            width=10
        )
        language_combo.pack(anchor="w", pady=(5, 0))
        language_combo.bind("<<ComboboxSelected>>", self.on_language_change)
        
        # Login form
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Username field
        ttk.Label(form_frame, text=self.app.language_service.translate("login.username")).pack(anchor="w")
        self.username_var = tk.StringVar(value="admin")  # Default for testing
        username_entry = ttk.Entry(form_frame, textvariable=self.username_var, font=("Segoe UI", 11))
        username_entry.pack(fill=tk.X, pady=(5, 15))
        
        # Password field
        ttk.Label(form_frame, text=self.app.language_service.translate("login.password")).pack(anchor="w")
        self.password_var = tk.StringVar(value="admin123")  # Default for testing
        password_entry = ttk.Entry(form_frame, textvariable=self.password_var, show="*", font=("Segoe UI", 11))
        password_entry.pack(fill=tk.X, pady=(5, 15))
        
        # Login button
        login_button = ttk.Button(
            form_frame,
            text=self.app.language_service.translate("login.login_button"),
            command=self.on_login_click,
            style="Accent.TButton"
        )
        login_button.pack(fill=tk.X, pady=(10, 0))
        
        # Forgot password link
        forgot_label = ttk.Label(
            form_frame,
            text=self.app.language_service.translate("login.forgot_password"),
            foreground="blue",
            cursor="hand2",
            font=("Segoe UI", 9, "underline")
        )
        forgot_label.pack(pady=(10, 0))
        forgot_label.bind("<Button-1>", self.on_forgot_password)
        
        # Bind Enter key to login
        self.root.bind('<Return>', lambda e: self.on_login_click())
        
        # Focus on username field
        username_entry.focus()
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def configure_style(self):
        """Configure TTK styles"""
        style = ttk.Style()
        
        # Configure accent button style
        style.configure(
            "Accent.TButton",
            font=("Segoe UI", 11, "bold"),
            padding=(10, 8)
        )
    
    def on_language_change(self, event=None):
        """Handle language change"""
        new_language = self.language_var.get()
        if self.app.language_service.set_language(new_language):
            # Recreate the window with new language
            self.root.destroy()
            self.create_login_window()
    
    def on_login_click(self):
        """Handle login button click"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        if not username or not password:
            messagebox.showerror(
                self.app.language_service.translate("common.error"),
                "Please enter both username and password"
            )
            return
        
        try:
            # Attempt authentication
            if self.authenticate_user(username, password):
                self.login_successful = True
                logger.info(f"User {username} logged in successfully")
                messagebox.showinfo(
                    self.app.language_service.translate("common.success"),
                    self.app.language_service.translate("login.login_success")
                )
                self.root.destroy()
            else:
                messagebox.showerror(
                    self.app.language_service.translate("common.error"),
                    self.app.language_service.translate("login.invalid_credentials")
                )
                
        except Exception as e:
            logger.error(f"Login error: {e}")
            messagebox.showerror(
                self.app.language_service.translate("common.error"),
                f"Login failed: {str(e)}"
            )
    
    def authenticate_user(self, username, password):
        """Authenticate user credentials"""
        try:
            # Simple authentication for now
            # In production, this should use proper password hashing
            if username == "admin" and password == "admin123":
                # Create a simple user object
                class User:
                    def __init__(self, username, email, first_name, last_name):
                        self.username = username
                        self.email = email
                        self.first_name = first_name
                        self.last_name = last_name
                
                self.app.current_user = User("admin", "<EMAIL>", "System", "Administrator")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def on_forgot_password(self, event=None):
        """Handle forgot password click"""
        messagebox.showinfo(
            "Forgot Password",
            "Please contact your system administrator to reset your password."
        )
    
    def on_window_close(self):
        """Handle window close event"""
        self.login_successful = False
        self.root.destroy()
