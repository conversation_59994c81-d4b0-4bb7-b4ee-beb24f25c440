"""
Database service for managing SQLite database operations
"""
import sqlite3
import os
import logging
from pathlib import Path
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseService:
    """Database service for SQLite operations"""
    
    def __init__(self, db_path="data/management.db"):
        self.db_path = db_path
        self.connection = None
        self.initialize_database()
    
    def initialize_database(self):
        """Initialize database connection and create tables if needed"""
        try:
            # Ensure data directory exists
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Create connection
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row  # Enable column access by name
            
            # Enable foreign key constraints
            self.connection.execute("PRAGMA foreign_keys = ON")
            
            # Create tables if they don't exist
            self.create_tables()
            
            # Insert default data if tables are empty
            self.insert_default_data()
            
            logger.info(f"Database initialized successfully: {self.db_path}")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def create_tables(self):
        """Create database tables"""
        try:
            # Read and execute schema from SQL file
            schema_path = Path(__file__).parent.parent / "database_schema.sql"
            if schema_path.exists():
                with open(schema_path, 'r', encoding='utf-8') as f:
                    schema_sql = f.read()
                
                # Split by semicolon and execute each statement
                statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
                for statement in statements:
                    if statement and not statement.startswith('--'):
                        try:
                            self.connection.execute(statement)
                        except sqlite3.Error as e:
                            # Ignore table already exists errors
                            if "already exists" not in str(e).lower():
                                logger.warning(f"SQL statement failed: {statement[:50]}... Error: {e}")
                
                self.connection.commit()
                logger.info("Database tables created successfully")
            else:
                # Create basic tables if schema file doesn't exist
                self.create_basic_tables()
                
        except Exception as e:
            logger.error(f"Failed to create tables: {e}")
            raise
    
    def create_basic_tables(self):
        """Create basic tables if schema file is not available"""
        basic_schema = """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            last_login DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            language_preference VARCHAR(5) DEFAULT 'en'
        );
        
        CREATE TABLE IF NOT EXISTS roles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(50) UNIQUE NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            session_token VARCHAR(255) UNIQUE NOT NULL,
            expires_at DATETIME NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        );
        """
        
        self.connection.executescript(basic_schema)
        self.connection.commit()
        logger.info("Basic database tables created")
    
    def insert_default_data(self):
        """Insert default system data if not exists"""
        try:
            # Check if admin user exists
            cursor = self.connection.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
            if cursor.fetchone()[0] == 0:
                # Insert default admin user (password: admin123)
                import bcrypt
                password_hash = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt())
                
                self.connection.execute("""
                    INSERT INTO users (username, email, password_hash, first_name, last_name, is_active)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, ("admin", "<EMAIL>", password_hash, "System", "Administrator", 1))
                
                # Insert default role if roles table exists
                try:
                    self.connection.execute("""
                        INSERT OR IGNORE INTO roles (name, description)
                        VALUES ('super_admin', 'Super Administrator with full access')
                    """)
                except sqlite3.Error:
                    pass  # Roles table might not exist yet
                
                self.connection.commit()
                logger.info("Default admin user created")
                
        except Exception as e:
            logger.warning(f"Could not insert default data: {e}")
    
    def execute_query(self, query, params=None):
        """Execute a query and return results"""
        try:
            if params:
                cursor = self.connection.execute(query, params)
            else:
                cursor = self.connection.execute(query)
            return cursor.fetchall()
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise
    
    def execute_update(self, query, params=None):
        """Execute an update/insert/delete query"""
        try:
            if params:
                cursor = self.connection.execute(query, params)
            else:
                cursor = self.connection.execute(query)
            self.connection.commit()
            return cursor.rowcount
        except Exception as e:
            logger.error(f"Update execution failed: {e}")
            self.connection.rollback()
            raise
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")
    
    def backup_database(self, backup_path=None):
        """Create database backup"""
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backups/backup_{timestamp}.db"
        
        try:
            Path(backup_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Copy database file
            import shutil
            shutil.copy2(self.db_path, backup_path)
            
            logger.info(f"Database backup created: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            raise
