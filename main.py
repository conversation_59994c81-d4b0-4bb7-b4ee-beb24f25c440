#!/usr/bin/env python3
"""
Management System - Main Application Entry Point

A comprehensive business management system built with Python and SQLite3.
Features multi-language support, role-based access control, and modern UI.

Author: Management System Development Team
Version: 1.0.0
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import application modules
from config.settings import load_config
from services.database_service import DatabaseService
from services.language_service import LanguageService
from views.login_view import LoginView
from utils.logging_config import setup_logging

class ManagementSystemApp:
    """Main application class"""
    
    def __init__(self):
        """Initialize the application"""
        self.config = load_config()
        self.setup_logging()
        self.setup_services()
        self.current_user = None
        self.main_window = None
        
    def setup_logging(self):
        """Setup application logging"""
        setup_logging(self.config.log_level)
        self.logger = logging.getLogger(__name__)
        self.logger.info("Management System starting up...")
        
    def setup_services(self):
        """Initialize core services"""
        try:
            # Initialize database service
            self.db_service = DatabaseService(self.config.database.path)
            self.logger.info("Database service initialized")
            
            # Initialize language service
            self.language_service = LanguageService()
            self.language_service.set_language(self.config.ui.default_language)
            self.logger.info("Language service initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize services: {e}")
            raise
    
    def run(self):
        """Run the application"""
        try:
            # Show login screen
            login_view = LoginView(self)
            
            # If login successful, show main application
            if login_view.show_login():
                self.show_main_application()
            else:
                self.logger.info("Application closed from login screen")
                
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            self.show_error_dialog("Application Error", str(e))
        finally:
            self.cleanup()
    
    def show_main_application(self):
        """Show the main application window"""
        from views.dashboard_view import DashboardView
        
        self.main_window = DashboardView(self)
        self.main_window.show()
        
    def authenticate_user(self, username, password):
        """Authenticate user credentials"""
        from services.auth_service import AuthenticationService
        
        auth_service = AuthenticationService(self.db_service)
        user, session_token = auth_service.authenticate_user(username, password)
        
        if user:
            self.current_user = user
            self.session_token = session_token
            self.logger.info(f"User {username} authenticated successfully")
            return True
        else:
            self.logger.warning(f"Authentication failed for user {username}")
            return False
    
    def logout(self):
        """Logout current user"""
        if self.current_user:
            self.logger.info(f"User {self.current_user.username} logged out")
            self.current_user = None
            self.session_token = None
            
            # Close main window and show login
            if self.main_window:
                self.main_window.close()
                self.main_window = None
            
            # Show login screen again
            login_view = LoginView(self)
            if login_view.show_login():
                self.show_main_application()
    
    def show_error_dialog(self, title, message):
        """Show error dialog"""
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.withdraw()  # Hide the root window
        messagebox.showerror(title, message)
        root.destroy()
    
    def cleanup(self):
        """Cleanup resources"""
        self.logger.info("Application shutting down...")
        
        # Close database connections
        if hasattr(self, 'db_service'):
            self.db_service.close()
        
        # Save user preferences
        self.save_user_preferences()
        
        self.logger.info("Application shutdown complete")
    
    def save_user_preferences(self):
        """Save user preferences"""
        if self.current_user:
            # Save language preference
            preferences = {
                'language': self.language_service.current_language,
                'theme': getattr(self.config.ui, 'theme', 'light')
            }
            
            # Update user preferences in database
            # Implementation here
            pass

def main():
    """Main entry point"""
    try:
        # Create and run application
        app = ManagementSystemApp()
        app.run()
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {e}")
        logging.error(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
