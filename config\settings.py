"""
Configuration settings for the Management System
"""
import os
from dataclasses import dataclass
from pathlib import Path

@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    path: str = "data/management.db"
    backup_path: str = "backups/"
    auto_backup: bool = True
    backup_interval: int = 24  # hours

@dataclass
class SecurityConfig:
    """Security configuration settings"""
    session_timeout: int = 120  # minutes
    max_login_attempts: int = 5
    lockout_duration: int = 30  # minutes
    password_min_length: int = 8
    require_special_chars: bool = True

@dataclass
class UIConfig:
    """UI configuration settings"""
    theme: str = "light"
    default_language: str = "en"
    window_width: int = 1200
    window_height: int = 800
    auto_save_interval: int = 300  # seconds

@dataclass
class AppConfig:
    """Main application configuration"""
    debug: bool = False
    log_level: str = "INFO"
    database: DatabaseConfig = None
    security: SecurityConfig = None
    ui: UIConfig = None
    
    def __post_init__(self):
        if self.database is None:
            self.database = DatabaseConfig()
        if self.security is None:
            self.security = SecurityConfig()
        if self.ui is None:
            self.ui = UIConfig()

def load_config():
    """Load configuration from environment or use defaults"""
    config = AppConfig()
    
    # Override with environment variables if they exist
    if os.getenv('DEBUG'):
        config.debug = os.getenv('DEBUG').lower() == 'true'
    
    if os.getenv('DATABASE_PATH'):
        config.database.path = os.getenv('DATABASE_PATH')
    
    if os.getenv('LOG_LEVEL'):
        config.log_level = os.getenv('LOG_LEVEL')
    
    # Ensure directories exist
    Path(config.database.path).parent.mkdir(parents=True, exist_ok=True)
    Path(config.database.backup_path).mkdir(parents=True, exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    return config
