# Comprehensive Management System Design Document

## Table of Contents
1. [System Overview](#system-overview)
2. [System Architecture](#system-architecture)
3. [Technology Stack](#technology-stack)
4. [Database Design](#database-design)
5. [User Interface Design](#user-interface-design)
6. [Module Specifications](#module-specifications)
7. [Authentication & Authorization](#authentication--authorization)
8. [Implementation Guidelines](#implementation-guidelines)
9. [Scalability & Maintenance](#scalability--maintenance)

## System Overview

### Project Description
A comprehensive business management system built with Python and SQLite3, featuring a modern GUI interface with multi-language support (Arabic/English). The system provides complete business process management including customer relations, inventory, sales, purchases, finance, and reporting.

### Key Features
- **Multi-language Support**: Arabic and English with RTL/LTR text direction
- **Role-based Access Control**: Granular permissions system
- **Modern UI**: Professional interface with icons and responsive design
- **Comprehensive Modules**: 12 integrated business management modules
- **Real-time Dashboard**: Charts, statistics, and KPIs
- **Document Management**: File upload and archiving system
- **Reporting Engine**: Customizable reports and analytics

### Target Users
- Small to medium businesses
- Retail stores and warehouses
- Service providers
- Manufacturing companies

## System Architecture

### Architecture Pattern
**Model-View-Controller (MVC) Pattern**
- **Model**: SQLite3 database with ORM layer
- **View**: Tkinter/CustomTkinter GUI components
- **Controller**: Business logic and event handlers

### System Components
```
┌─────────────────────────────────────────┐
│              Presentation Layer          │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │   Login     │  │    Main Dashboard   ││
│  │   Screen    │  │                     ││
│  └─────────────┘  └─────────────────────┘│
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              Business Layer             │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │ Controllers │  │   Service Classes   ││
│  │             │  │                     ││
│  └─────────────┘  └─────────────────────┘│
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│               Data Layer                │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │   Models    │  │   SQLite3 Database  ││
│  │             │  │                     ││
│  └─────────────┘  └─────────────────────┘│
└─────────────────────────────────────────┘
```

## Technology Stack

### Core Technologies
- **Programming Language**: Python 3.9+
- **Database**: SQLite3
- **GUI Framework**: CustomTkinter (modern Tkinter alternative)
- **ORM**: SQLAlchemy
- **Charts/Graphs**: Matplotlib + Tkinter integration
- **Icons**: Pillow (PIL) for image handling
- **Reports**: ReportLab for PDF generation

### Additional Libraries
```python
# requirements.txt
customtkinter==5.2.0
sqlalchemy==2.0.23
pillow==10.1.0
matplotlib==3.8.2
reportlab==4.0.7
babel==2.13.1
python-dateutil==2.8.2
openpyxl==3.1.2
```

### Project Structure
```
management_system/
├── main.py                 # Application entry point
├── config/
│   ├── __init__.py
│   ├── settings.py         # Configuration settings
│   └── database.py         # Database configuration
├── models/
│   ├── __init__.py
│   ├── base.py            # Base model class
│   ├── user.py            # User and authentication models
│   ├── customer.py        # Customer models
│   ├── product.py         # Product and inventory models
│   ├── sales.py           # Sales transaction models
│   ├── purchase.py        # Purchase models
│   ├── supplier.py        # Supplier models
│   ├── employee.py        # Employee models
│   ├── finance.py         # Financial models
│   └── document.py        # Document management models
├── views/
│   ├── __init__.py
│   ├── base_view.py       # Base view class
│   ├── login_view.py      # Login screen
│   ├── dashboard_view.py  # Main dashboard
│   ├── customer_view.py   # Customer management
│   ├── inventory_view.py  # Inventory management
│   ├── sales_view.py      # Sales management
│   ├── purchase_view.py   # Purchase management
│   ├── supplier_view.py   # Supplier management
│   ├── employee_view.py   # Employee management
│   ├── finance_view.py    # Finance management
│   ├── invoice_view.py    # Invoice management
│   ├── report_view.py     # Reports
│   └── settings_view.py   # System settings
├── controllers/
│   ├── __init__.py
│   ├── auth_controller.py # Authentication logic
│   ├── customer_controller.py
│   ├── inventory_controller.py
│   ├── sales_controller.py
│   ├── purchase_controller.py
│   ├── supplier_controller.py
│   ├── employee_controller.py
│   ├── finance_controller.py
│   ├── invoice_controller.py
│   └── report_controller.py
├── services/
│   ├── __init__.py
│   ├── database_service.py
│   ├── auth_service.py
│   ├── report_service.py
│   ├── backup_service.py
│   └── language_service.py
├── utils/
│   ├── __init__.py
│   ├── validators.py      # Input validation
│   ├── formatters.py      # Data formatting
│   ├── constants.py       # Application constants
│   └── helpers.py         # Utility functions
├── resources/
│   ├── icons/            # UI icons
│   ├── images/           # Application images
│   ├── languages/        # Translation files
│   │   ├── en.json       # English translations
│   │   └── ar.json       # Arabic translations
│   └── themes/           # UI themes
├── data/
│   └── management.db     # SQLite database file
├── reports/              # Generated reports
├── uploads/              # Uploaded documents
├── backups/              # Database backups
└── tests/               # Unit tests
    ├── __init__.py
    ├── test_models.py
    ├── test_controllers.py
    └── test_services.py
```

## Database Design

### Core Tables Overview
The database consists of 15+ interconnected tables supporting all business operations:

1. **Authentication & Users**
   - users
   - roles
   - permissions
   - user_roles

2. **Customer Management**
   - customers
   - customer_contacts
   - customer_addresses

3. **Supplier Management**
   - suppliers
   - supplier_contacts

4. **Product & Inventory**
   - categories
   - products
   - inventory
   - stock_movements

5. **Sales & Purchases**
   - sales_orders
   - sales_order_items
   - purchase_orders
   - purchase_order_items

6. **Financial Management**
   - accounts
   - transactions
   - invoices
   - payments

7. **Employee Management**
   - employees
   - departments
   - employee_roles

8. **Document Management**
   - documents
   - document_categories

### Key Relationships
- One-to-Many: Customer → Sales Orders
- Many-to-Many: Products ↔ Categories
- One-to-One: Employee → User Account
- Many-to-One: Transactions → Accounts

## User Interface Design

### Design Principles
1. **Clean & Modern**: Minimalist design with proper spacing
2. **Intuitive Navigation**: Logical menu structure and breadcrumbs
3. **Responsive Layout**: Adapts to different screen sizes
4. **Accessibility**: High contrast, readable fonts, keyboard navigation
5. **Consistency**: Uniform styling across all modules

### Color Scheme
- **Primary**: #2E86AB (Professional Blue)
- **Secondary**: #A23B72 (Accent Purple)
- **Success**: #F18F01 (Orange)
- **Warning**: #C73E1D (Red)
- **Background**: #F5F5F5 (Light Gray)
- **Text**: #333333 (Dark Gray)

### Typography
- **Headers**: Segoe UI, 16-24px, Bold
- **Body Text**: Segoe UI, 12-14px, Regular
- **Arabic Support**: Tahoma, Arial Unicode MS

### Login Screen Design
```
┌─────────────────────────────────────────┐
│              COMPANY LOGO               │
│                                         │
│        Management System v1.0          │
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ Language: [English ▼] [العربية ▼]  ││
│  └─────────────────────────────────────┘│
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ 👤 Username: [________________]     ││
│  └─────────────────────────────────────┘│
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ 🔒 Password: [________________]     ││
│  └─────────────────────────────────────┘│
│                                         │
│  ┌─────────────────────────────────────┐│
│  │          [  LOGIN  ]                ││
│  └─────────────────────────────────────┘│
│                                         │
│         Forgot Password?                │
└─────────────────────────────────────────┘
```

## Detailed Database Schema

### 1. Authentication & User Management Tables

```sql
-- Users table
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    last_login DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    language_preference VARCHAR(5) DEFAULT 'en'
);

-- Roles table
CREATE TABLE roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Permissions table
CREATE TABLE permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL
);

-- User roles junction table
CREATE TABLE user_roles (
    user_id INTEGER,
    role_id INTEGER,
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    assigned_by INTEGER,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id)
);

-- Role permissions junction table
CREATE TABLE role_permissions (
    role_id INTEGER,
    permission_id INTEGER,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);
```

### 2. Customer Management Tables

```sql
-- Customers table
CREATE TABLE customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_code VARCHAR(20) UNIQUE NOT NULL,
    company_name VARCHAR(100),
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    tax_number VARCHAR(50),
    credit_limit DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    customer_type VARCHAR(20) DEFAULT 'individual', -- individual, company
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, blocked
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Customer addresses table
CREATE TABLE customer_addresses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    address_type VARCHAR(20) DEFAULT 'billing', -- billing, shipping, both
    street_address TEXT NOT NULL,
    city VARCHAR(50) NOT NULL,
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50) NOT NULL,
    is_default BOOLEAN DEFAULT 0,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- Customer contacts table
CREATE TABLE customer_contacts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    contact_name VARCHAR(100) NOT NULL,
    position VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    is_primary BOOLEAN DEFAULT 0,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);
```

### 3. Supplier Management Tables

```sql
-- Suppliers table
CREATE TABLE suppliers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supplier_code VARCHAR(20) UNIQUE NOT NULL,
    company_name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    tax_number VARCHAR(50),
    payment_terms INTEGER DEFAULT 30, -- days
    current_balance DECIMAL(15,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    rating INTEGER DEFAULT 0, -- 1-5 stars
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Supplier addresses table
CREATE TABLE supplier_addresses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supplier_id INTEGER NOT NULL,
    street_address TEXT NOT NULL,
    city VARCHAR(50) NOT NULL,
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50) NOT NULL,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE
);

-- Supplier contacts table
CREATE TABLE supplier_contacts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supplier_id INTEGER NOT NULL,
    contact_name VARCHAR(100) NOT NULL,
    position VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    is_primary BOOLEAN DEFAULT 0,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE
);
```

### 4. Product & Inventory Management Tables

```sql
-- Categories table
CREATE TABLE categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INTEGER,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id)
);

-- Products table
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_code VARCHAR(50) UNIQUE NOT NULL,
    barcode VARCHAR(100),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INTEGER,
    unit_of_measure VARCHAR(20) DEFAULT 'piece',
    cost_price DECIMAL(15,2) DEFAULT 0,
    selling_price DECIMAL(15,2) DEFAULT 0,
    min_stock_level INTEGER DEFAULT 0,
    max_stock_level INTEGER DEFAULT 0,
    reorder_point INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    has_variants BOOLEAN DEFAULT 0,
    track_inventory BOOLEAN DEFAULT 1,
    weight DECIMAL(10,3),
    dimensions VARCHAR(50),
    image_path VARCHAR(255),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (category_id) REFERENCES categories(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Product variants table
CREATE TABLE product_variants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    parent_product_id INTEGER NOT NULL,
    variant_code VARCHAR(50) UNIQUE NOT NULL,
    variant_name VARCHAR(200) NOT NULL,
    cost_price DECIMAL(15,2) DEFAULT 0,
    selling_price DECIMAL(15,2) DEFAULT 0,
    barcode VARCHAR(100),
    is_active BOOLEAN DEFAULT 1,
    FOREIGN KEY (parent_product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Inventory table
CREATE TABLE inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    variant_id INTEGER,
    current_stock INTEGER DEFAULT 0,
    reserved_stock INTEGER DEFAULT 0,
    available_stock INTEGER GENERATED ALWAYS AS (current_stock - reserved_stock) STORED,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (variant_id) REFERENCES product_variants(id) ON DELETE CASCADE,
    UNIQUE(product_id, variant_id)
);

-- Stock movements table
CREATE TABLE stock_movements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    variant_id INTEGER,
    movement_type VARCHAR(20) NOT NULL, -- in, out, adjustment, transfer
    quantity INTEGER NOT NULL,
    reference_type VARCHAR(50), -- purchase, sale, adjustment, transfer
    reference_id INTEGER,
    notes TEXT,
    movement_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (variant_id) REFERENCES product_variants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

### 5. Sales Management Tables

```sql
-- Sales orders table
CREATE TABLE sales_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INTEGER NOT NULL,
    order_date DATE NOT NULL,
    due_date DATE,
    status VARCHAR(20) DEFAULT 'draft', -- draft, confirmed, shipped, delivered, cancelled
    subtotal DECIMAL(15,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    balance_due DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    payment_status VARCHAR(20) DEFAULT 'pending', -- pending, partial, paid, overdue
    shipping_address TEXT,
    billing_address TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Sales order items table
CREATE TABLE sales_order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    variant_id INTEGER,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    line_total DECIMAL(15,2) GENERATED ALWAYS AS (quantity * unit_price - discount_amount) STORED,
    FOREIGN KEY (order_id) REFERENCES sales_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (variant_id) REFERENCES product_variants(id)
);
```

### 6. Purchase Management Tables

```sql
-- Purchase orders table
CREATE TABLE purchase_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id INTEGER NOT NULL,
    order_date DATE NOT NULL,
    expected_date DATE,
    status VARCHAR(20) DEFAULT 'draft', -- draft, sent, confirmed, received, cancelled
    subtotal DECIMAL(15,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    balance_due DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    payment_status VARCHAR(20) DEFAULT 'pending',
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Purchase order items table
CREATE TABLE purchase_order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    variant_id INTEGER,
    quantity INTEGER NOT NULL,
    unit_cost DECIMAL(15,2) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    line_total DECIMAL(15,2) GENERATED ALWAYS AS (quantity * unit_cost - discount_amount) STORED,
    received_quantity INTEGER DEFAULT 0,
    FOREIGN KEY (order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (variant_id) REFERENCES product_variants(id)
);
```

### 7. Financial Management Tables

```sql
-- Chart of accounts table
CREATE TABLE accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_code VARCHAR(20) UNIQUE NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    account_type VARCHAR(50) NOT NULL, -- asset, liability, equity, revenue, expense
    parent_account_id INTEGER,
    is_active BOOLEAN DEFAULT 1,
    balance DECIMAL(15,2) DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_account_id) REFERENCES accounts(id)
);

-- Financial transactions table
CREATE TABLE transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_number VARCHAR(50) UNIQUE NOT NULL,
    transaction_date DATE NOT NULL,
    description TEXT NOT NULL,
    reference_type VARCHAR(50), -- invoice, payment, adjustment
    reference_id INTEGER,
    total_amount DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'posted', -- draft, posted, reversed
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Transaction entries table (double-entry bookkeeping)
CREATE TABLE transaction_entries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_id INTEGER NOT NULL,
    account_id INTEGER NOT NULL,
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    description TEXT,
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES accounts(id)
);

-- Invoices table
CREATE TABLE invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    invoice_type VARCHAR(20) NOT NULL, -- sales, purchase
    customer_id INTEGER,
    supplier_id INTEGER,
    order_id INTEGER,
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(15,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    balance_due DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    status VARCHAR(20) DEFAULT 'draft', -- draft, sent, paid, overdue, cancelled
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Payments table
CREATE TABLE payments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    payment_number VARCHAR(50) UNIQUE NOT NULL,
    payment_type VARCHAR(20) NOT NULL, -- received, made
    customer_id INTEGER,
    supplier_id INTEGER,
    invoice_id INTEGER,
    payment_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL, -- cash, check, bank_transfer, credit_card
    reference_number VARCHAR(100),
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

### 8. Employee Management Tables

```sql
-- Departments table
CREATE TABLE departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    manager_id INTEGER,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (manager_id) REFERENCES employees(id)
);

-- Employees table
CREATE TABLE employees (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_code VARCHAR(20) UNIQUE NOT NULL,
    user_id INTEGER UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    department_id INTEGER,
    position VARCHAR(100),
    hire_date DATE NOT NULL,
    salary DECIMAL(15,2),
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, terminated
    address TEXT,
    emergency_contact VARCHAR(200),
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (department_id) REFERENCES departments(id)
);
```

### 9. Document Management Tables

```sql
-- Document categories table
CREATE TABLE document_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT 1
);

-- Documents table
CREATE TABLE documents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INTEGER,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    reference_type VARCHAR(50), -- customer, supplier, employee, product, order
    reference_id INTEGER,
    is_public BOOLEAN DEFAULT 0,
    uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INTEGER,
    FOREIGN KEY (category_id) REFERENCES document_categories(id),
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);
```

### Database Indexes for Performance

```sql
-- Performance indexes
CREATE INDEX idx_customers_code ON customers(customer_code);
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_products_code ON products(product_code);
CREATE INDEX idx_products_barcode ON products(barcode);
CREATE INDEX idx_sales_orders_number ON sales_orders(order_number);
CREATE INDEX idx_sales_orders_customer ON sales_orders(customer_id);
CREATE INDEX idx_sales_orders_date ON sales_orders(order_date);
CREATE INDEX idx_purchase_orders_number ON purchase_orders(order_number);
CREATE INDEX idx_purchase_orders_supplier ON purchase_orders(supplier_id);
CREATE INDEX idx_invoices_number ON invoices(invoice_number);
CREATE INDEX idx_invoices_date ON invoices(invoice_date);
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_stock_movements_product ON stock_movements(product_id);
CREATE INDEX idx_stock_movements_date ON stock_movements(movement_date);
```

## Main Dashboard Design

### Dashboard Layout
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [☰] Management System    [🔍 Search...]    [🌐 EN] [👤 Admin] [⚙️] [🚪]    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────┐ │
│  │ 👥 Customers    │  │ 📦 Products     │  │ 💰 Sales        │  │ 📊 KPIs │ │
│  │ Total: 1,234    │  │ Total: 567      │  │ Today: $12,345  │  │         │ │
│  │ New: +23        │  │ Low Stock: 12   │  │ Month: $456,789 │  │ [Chart] │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────┘ │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────┐ │
│  │ 🏪 Suppliers    │  │ 📋 Orders       │  │ 💳 Payments     │  │ 📈 Trend│ │
│  │ Total: 89       │  │ Pending: 15     │  │ Due: $23,456    │  │         │ │
│  │ Active: 67      │  │ Shipped: 8      │  │ Overdue: $1,234 │  │ [Chart] │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────┘ │
│                                                                             │
│ ┌─────────────────────────────────────────┐ ┌─────────────────────────────┐ │
│ │           Recent Activities             │ │        Quick Actions        │ │
│ │ ┌─────────────────────────────────────┐ │ │ [+ New Sale]   [+ Customer] │ │
│ │ │ • New order #1234 from ABC Corp     │ │ │ [+ Purchase]   [+ Product]  │ │
│ │ │ • Payment received $5,000           │ │ │ [+ Invoice]    [+ Supplier] │ │
│ │ │ • Low stock alert: Product XYZ      │ │ │ [📊 Reports]   [⚙️ Settings] │ │
│ │ │ • New customer registered           │ │ │                             │ │
│ │ └─────────────────────────────────────┘ │ └─────────────────────────────┘ │
│ └─────────────────────────────────────────┘                               │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Navigation Menu Structure
```
☰ Main Menu
├── 🏠 Dashboard
├── 👥 Customer Management
│   ├── Customer List
│   ├── Add New Customer
│   ├── Customer Groups
│   └── Customer Reports
├── 📦 Inventory Management
│   ├── Product List
│   ├── Add New Product
│   ├── Categories
│   ├── Stock Movements
│   ├── Stock Adjustments
│   └── Inventory Reports
├── 💰 Sales Management
│   ├── Sales Orders
│   ├── New Sale
│   ├── Quotations
│   ├── Sales Returns
│   └── Sales Reports
├── 🛒 Purchase Management
│   ├── Purchase Orders
│   ├── New Purchase
│   ├── Purchase Returns
│   └── Purchase Reports
├── 🏪 Supplier Management
│   ├── Supplier List
│   ├── Add New Supplier
│   └── Supplier Reports
├── 👨‍💼 Employee Management
│   ├── Employee List
│   ├── Add New Employee
│   ├── Departments
│   └── Employee Reports
├── 💳 Finance Management
│   ├── Chart of Accounts
│   ├── Transactions
│   ├── Payments
│   ├── Financial Reports
│   └── Budget Planning
├── 🧾 Invoice Management
│   ├── Sales Invoices
│   ├── Purchase Invoices
│   ├── Invoice Templates
│   └── Invoice Reports
├── 📊 Reports
│   ├── Sales Reports
│   ├── Purchase Reports
│   ├── Inventory Reports
│   ├── Financial Reports
│   ├── Customer Reports
│   └── Custom Reports
├── 📁 Document Management
│   ├── Document Library
│   ├── Upload Documents
│   ├── Categories
│   └── Archive
├── ⚙️ System Settings
│   ├── Company Settings
│   ├── User Management
│   ├── Roles & Permissions
│   ├── System Preferences
│   ├── Database Management
│   └── Backup & Restore
└── 🚪 Logout
```

### Language Support Implementation

#### Translation System
```python
# Language configuration
SUPPORTED_LANGUAGES = {
    'en': {
        'name': 'English',
        'direction': 'ltr',
        'font': 'Segoe UI'
    },
    'ar': {
        'name': 'العربية',
        'direction': 'rtl',
        'font': 'Tahoma'
    }
}

# Translation files structure
# resources/languages/en.json
{
    "login": {
        "title": "Management System Login",
        "username": "Username",
        "password": "Password",
        "login_button": "Login",
        "forgot_password": "Forgot Password?",
        "language": "Language"
    },
    "dashboard": {
        "title": "Dashboard",
        "customers": "Customers",
        "products": "Products",
        "sales": "Sales",
        "suppliers": "Suppliers"
    }
}

# resources/languages/ar.json
{
    "login": {
        "title": "تسجيل دخول نظام الإدارة",
        "username": "اسم المستخدم",
        "password": "كلمة المرور",
        "login_button": "تسجيل الدخول",
        "forgot_password": "نسيت كلمة المرور؟",
        "language": "اللغة"
    },
    "dashboard": {
        "title": "لوحة التحكم",
        "customers": "العملاء",
        "products": "المنتجات",
        "sales": "المبيعات",
        "suppliers": "الموردين"
    }
}
```

### Form Design Standards

#### Standard Form Layout
```
┌─────────────────────────────────────────────────────────────┐
│ [Module Name] - [Action] (Add/Edit/View)          [X] Close │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                    Basic Information                    │ │
│ │ ┌─────────────────┐  ┌─────────────────────────────────┐ │ │
│ │ │ Field Label:    │  │ [Input Field]                   │ │ │
│ │ └─────────────────┘  └─────────────────────────────────┘ │ │
│ │ ┌─────────────────┐  ┌─────────────────────────────────┐ │ │
│ │ │ Field Label:    │  │ [Input Field]                   │ │ │
│ │ └─────────────────┘  └─────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                   Additional Details                    │ │
│ │ [Expandable section with more fields]                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                      Actions                            │ │
│ │    [Save] [Save & New] [Cancel] [Delete] [Print]        │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### Data Grid Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [Module] List                    [🔍 Search] [🔽 Filter] [+ Add New]        │
├─────────────────────────────────────────────────────────────────────────────┤
│ ☑ │ ID   │ Name/Description      │ Status    │ Date       │ Actions         │
├───┼──────┼──────────────────────┼───────────┼────────────┼─────────────────┤
│ ☑ │ 001  │ Customer ABC Corp    │ Active    │ 2024-01-15 │ [👁️] [✏️] [🗑️] │
│ ☑ │ 002  │ Product XYZ          │ Active    │ 2024-01-14 │ [👁️] [✏️] [🗑️] │
│ ☑ │ 003  │ Order #12345         │ Pending   │ 2024-01-13 │ [👁️] [✏️] [🗑️] │
├───┼──────┼──────────────────────┼───────────┼────────────┼─────────────────┤
│                                                                             │
│ Showing 1-25 of 150 records    [◀️ Previous] [1][2][3]...[6] [Next ▶️]     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Detailed Module Specifications

### 1. Customer Management Module

#### Core Features
- **Customer Registration**: Complete customer profiles with contact information
- **Customer Categories**: Classify customers (VIP, Regular, Wholesale, Retail)
- **Credit Management**: Set credit limits and track outstanding balances
- **Contact Management**: Multiple contacts per customer
- **Address Management**: Billing and shipping addresses
- **Customer History**: Transaction history and interaction logs
- **Customer Reports**: Detailed analytics and performance metrics

#### Key Functionalities
```python
class CustomerModule:
    def create_customer(self, customer_data):
        """Create new customer with validation"""
        pass

    def update_customer(self, customer_id, updates):
        """Update customer information"""
        pass

    def get_customer_balance(self, customer_id):
        """Calculate current customer balance"""
        pass

    def get_customer_history(self, customer_id, date_range):
        """Retrieve customer transaction history"""
        pass

    def generate_customer_statement(self, customer_id):
        """Generate customer account statement"""
        pass
```

#### Customer Form Fields
- Basic Info: Name, Company, Email, Phone, Mobile
- Business Info: Tax Number, Credit Limit, Payment Terms
- Addresses: Billing Address, Shipping Address(es)
- Contacts: Primary Contact, Additional Contacts
- Settings: Customer Type, Status, Notes

### 2. Inventory/Warehouse Management Module

#### Core Features
- **Product Catalog**: Comprehensive product database with variants
- **Category Management**: Hierarchical product categorization
- **Stock Tracking**: Real-time inventory levels and movements
- **Barcode Support**: Barcode generation and scanning
- **Multi-location**: Support for multiple warehouses/locations
- **Stock Alerts**: Low stock and reorder point notifications
- **Inventory Adjustments**: Stock corrections and write-offs
- **Batch/Serial Tracking**: Track products by batch or serial numbers

#### Key Functionalities
```python
class InventoryModule:
    def add_product(self, product_data):
        """Add new product to inventory"""
        pass

    def update_stock(self, product_id, quantity, movement_type):
        """Update stock levels with movement tracking"""
        pass

    def check_stock_availability(self, product_id, required_quantity):
        """Check if sufficient stock is available"""
        pass

    def generate_low_stock_report(self):
        """Generate report of products below reorder point"""
        pass

    def perform_stock_adjustment(self, adjustments):
        """Perform bulk stock adjustments"""
        pass
```

#### Product Form Fields
- Basic Info: Name, Code, Barcode, Description
- Pricing: Cost Price, Selling Price, Discount Rules
- Inventory: Current Stock, Min/Max Levels, Reorder Point
- Details: Category, Unit of Measure, Weight, Dimensions
- Settings: Track Inventory, Has Variants, Status

### 3. Sales Management Module

#### Core Features
- **Sales Order Processing**: Complete order lifecycle management
- **Quotation Management**: Create and convert quotes to orders
- **Customer Portal**: Customer self-service capabilities
- **Sales Analytics**: Performance tracking and forecasting
- **Commission Tracking**: Sales representative commission calculation
- **Return Management**: Handle sales returns and refunds
- **Pricing Rules**: Dynamic pricing based on customer/quantity
- **Sales Workflow**: Customizable approval workflows

#### Key Functionalities
```python
class SalesModule:
    def create_sales_order(self, order_data):
        """Create new sales order"""
        pass

    def process_payment(self, order_id, payment_data):
        """Process payment for sales order"""
        pass

    def generate_invoice(self, order_id):
        """Generate invoice from sales order"""
        pass

    def calculate_commission(self, salesperson_id, period):
        """Calculate sales commission"""
        pass

    def process_return(self, return_data):
        """Process sales return"""
        pass
```

#### Sales Order Form
- Customer Info: Customer Selection, Billing/Shipping Address
- Order Details: Order Date, Due Date, Reference Number
- Line Items: Product, Quantity, Price, Discount, Total
- Totals: Subtotal, Tax, Discount, Grand Total
- Payment: Payment Terms, Payment Status
- Notes: Internal Notes, Customer Instructions

### 4. Purchase Management Module

#### Core Features
- **Purchase Order Management**: Complete procurement workflow
- **Supplier Management**: Vendor performance tracking
- **Purchase Requisitions**: Internal purchase requests
- **Receiving Management**: Track deliveries and partial receipts
- **Purchase Analytics**: Cost analysis and supplier performance
- **Budget Control**: Purchase budget monitoring
- **Approval Workflow**: Multi-level purchase approvals
- **Contract Management**: Supplier contracts and agreements

#### Key Functionalities
```python
class PurchaseModule:
    def create_purchase_order(self, order_data):
        """Create new purchase order"""
        pass

    def receive_goods(self, order_id, received_items):
        """Process goods receipt"""
        pass

    def process_supplier_invoice(self, invoice_data):
        """Process supplier invoice"""
        pass

    def evaluate_supplier_performance(self, supplier_id):
        """Evaluate supplier performance metrics"""
        pass
```

### 5. Supplier Management Module

#### Core Features
- **Supplier Database**: Comprehensive supplier profiles
- **Performance Tracking**: Quality, delivery, and cost metrics
- **Contract Management**: Supplier agreements and terms
- **Payment Terms**: Flexible payment arrangements
- **Supplier Portal**: Self-service supplier interface
- **Qualification Process**: Supplier approval workflow
- **Risk Assessment**: Supplier risk evaluation
- **Communication Log**: Interaction history tracking

### 6. Employee Management Module

#### Core Features
- **Employee Profiles**: Complete HR information management
- **Department Structure**: Organizational hierarchy
- **Role Management**: Job roles and responsibilities
- **Attendance Tracking**: Time and attendance monitoring
- **Performance Management**: Employee evaluation system
- **Payroll Integration**: Salary and benefits management
- **Document Management**: Employee document storage
- **Training Records**: Skills and training tracking

### 7. Finance/Accounts Management Module

#### Core Features
- **Chart of Accounts**: Flexible account structure
- **Double-Entry Bookkeeping**: Complete financial recording
- **Bank Reconciliation**: Automated bank statement matching
- **Financial Reporting**: P&L, Balance Sheet, Cash Flow
- **Budget Management**: Budget planning and variance analysis
- **Tax Management**: Tax calculation and reporting
- **Multi-Currency**: Support for multiple currencies
- **Audit Trail**: Complete transaction history

#### Key Functionalities
```python
class FinanceModule:
    def create_journal_entry(self, entry_data):
        """Create double-entry journal entry"""
        pass

    def reconcile_bank_account(self, account_id, statement_data):
        """Reconcile bank account with statement"""
        pass

    def generate_financial_report(self, report_type, period):
        """Generate financial reports"""
        pass

    def calculate_tax(self, transaction_data):
        """Calculate applicable taxes"""
        pass
```

### 8. Invoice Management Module

#### Core Features
- **Invoice Generation**: Automated invoice creation
- **Template Management**: Customizable invoice templates
- **Payment Tracking**: Payment status monitoring
- **Recurring Invoices**: Automated recurring billing
- **Credit Notes**: Handle returns and adjustments
- **Multi-Language**: Invoices in multiple languages
- **Digital Signatures**: Electronic invoice signing
- **Integration**: Email and payment gateway integration

### 9. Reports Management Module

#### Core Features
- **Standard Reports**: Pre-built business reports
- **Custom Reports**: User-defined report builder
- **Dashboard Analytics**: Real-time KPI monitoring
- **Scheduled Reports**: Automated report generation
- **Export Options**: PDF, Excel, CSV export formats
- **Report Security**: Role-based report access
- **Data Visualization**: Charts and graphs
- **Drill-Down**: Interactive report navigation

#### Report Categories
1. **Sales Reports**
   - Sales Summary by Period
   - Customer Sales Analysis
   - Product Performance
   - Sales Representative Performance
   - Sales Forecast

2. **Purchase Reports**
   - Purchase Summary
   - Supplier Performance
   - Cost Analysis
   - Purchase Trends

3. **Inventory Reports**
   - Stock Status Report
   - Stock Movement Report
   - Reorder Report
   - Inventory Valuation

4. **Financial Reports**
   - Profit & Loss Statement
   - Balance Sheet
   - Cash Flow Statement
   - Accounts Receivable/Payable
   - Budget vs Actual

### 10. Document Management Module

#### Core Features
- **File Upload**: Support for multiple file formats
- **Document Categorization**: Organized folder structure
- **Version Control**: Document version tracking
- **Access Control**: Role-based document access
- **Search Functionality**: Full-text document search
- **Document Linking**: Link documents to records
- **Backup & Archive**: Automated document backup
- **Digital Signatures**: Document signing workflow

## Authentication & Authorization System

### Authentication Design

#### Login Process Flow
```
1. User enters credentials (username/password)
2. System validates credentials against database
3. Password verification using secure hashing (bcrypt)
4. Session creation with unique token
5. User preferences loaded (language, theme)
6. Redirect to dashboard with appropriate permissions
```

#### Security Features
- **Password Hashing**: bcrypt with salt for secure password storage
- **Session Management**: Secure session tokens with expiration
- **Account Lockout**: Temporary lockout after failed attempts
- **Password Policy**: Minimum complexity requirements
- **Two-Factor Authentication**: Optional 2FA support
- **Audit Logging**: Complete authentication activity log
- **Session Timeout**: Automatic logout after inactivity

#### Authentication Implementation
```python
import bcrypt
import secrets
from datetime import datetime, timedelta

class AuthenticationService:
    def __init__(self):
        self.max_login_attempts = 5
        self.lockout_duration = 30  # minutes
        self.session_timeout = 120  # minutes

    def hash_password(self, password):
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt)

    def verify_password(self, password, hashed):
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed)

    def authenticate_user(self, username, password):
        """Authenticate user credentials"""
        user = self.get_user_by_username(username)

        if not user:
            return None, "Invalid username or password"

        if self.is_account_locked(user.id):
            return None, "Account temporarily locked"

        if not self.verify_password(password, user.password_hash):
            self.record_failed_attempt(user.id)
            return None, "Invalid username or password"

        # Reset failed attempts on successful login
        self.reset_failed_attempts(user.id)

        # Create session
        session_token = self.create_session(user.id)

        # Update last login
        self.update_last_login(user.id)

        return user, session_token

    def create_session(self, user_id):
        """Create secure session token"""
        token = secrets.token_urlsafe(32)
        expires_at = datetime.now() + timedelta(minutes=self.session_timeout)

        # Store session in database
        self.store_session(user_id, token, expires_at)

        return token

    def validate_session(self, token):
        """Validate session token"""
        session = self.get_session_by_token(token)

        if not session:
            return None

        if datetime.now() > session.expires_at:
            self.delete_session(token)
            return None

        # Extend session if valid
        self.extend_session(token)

        return session.user_id
```

### Authorization System

#### Role-Based Access Control (RBAC)
The system implements a flexible RBAC model with the following components:

1. **Users**: Individual system users
2. **Roles**: Collections of permissions (Admin, Manager, Sales, Accountant)
3. **Permissions**: Specific actions on resources (create, read, update, delete)
4. **Resources**: System modules and data entities

#### Default Roles and Permissions

```python
DEFAULT_ROLES = {
    'super_admin': {
        'name': 'Super Administrator',
        'description': 'Full system access',
        'permissions': ['*']  # All permissions
    },
    'admin': {
        'name': 'Administrator',
        'description': 'System administration',
        'permissions': [
            'users.create', 'users.read', 'users.update', 'users.delete',
            'roles.create', 'roles.read', 'roles.update', 'roles.delete',
            'system.backup', 'system.restore', 'system.settings'
        ]
    },
    'manager': {
        'name': 'Manager',
        'description': 'Business operations management',
        'permissions': [
            'customers.*', 'suppliers.*', 'products.*',
            'sales.*', 'purchases.*', 'inventory.*',
            'reports.read', 'dashboard.read'
        ]
    },
    'sales_rep': {
        'name': 'Sales Representative',
        'description': 'Sales operations',
        'permissions': [
            'customers.read', 'customers.create', 'customers.update',
            'sales.*', 'products.read', 'inventory.read',
            'reports.sales', 'dashboard.read'
        ]
    },
    'accountant': {
        'name': 'Accountant',
        'description': 'Financial operations',
        'permissions': [
            'accounts.*', 'transactions.*', 'invoices.*',
            'payments.*', 'reports.financial', 'dashboard.read'
        ]
    },
    'warehouse_staff': {
        'name': 'Warehouse Staff',
        'description': 'Inventory management',
        'permissions': [
            'products.read', 'products.update',
            'inventory.*', 'stock_movements.*',
            'reports.inventory', 'dashboard.read'
        ]
    }
}
```

#### Permission System
```python
class PermissionService:
    def __init__(self):
        self.permissions_cache = {}

    def check_permission(self, user_id, permission):
        """Check if user has specific permission"""
        user_permissions = self.get_user_permissions(user_id)

        # Check for wildcard permissions
        if '*' in user_permissions:
            return True

        # Check for module wildcard (e.g., 'customers.*')
        module = permission.split('.')[0]
        if f"{module}.*" in user_permissions:
            return True

        # Check for exact permission
        return permission in user_permissions

    def get_user_permissions(self, user_id):
        """Get all permissions for a user"""
        if user_id in self.permissions_cache:
            return self.permissions_cache[user_id]

        permissions = set()
        user_roles = self.get_user_roles(user_id)

        for role in user_roles:
            role_permissions = self.get_role_permissions(role.id)
            permissions.update(role_permissions)

        self.permissions_cache[user_id] = permissions
        return permissions

    def require_permission(self, permission):
        """Decorator to require specific permission"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                current_user = self.get_current_user()
                if not self.check_permission(current_user.id, permission):
                    raise PermissionError(f"Permission denied: {permission}")
                return func(*args, **kwargs)
            return wrapper
        return decorator
```

#### Module-Level Security
```python
# Example: Customer module with permission checks
class CustomerController:
    def __init__(self):
        self.permission_service = PermissionService()

    @require_permission('customers.create')
    def create_customer(self, customer_data):
        """Create new customer - requires create permission"""
        return self.customer_service.create(customer_data)

    @require_permission('customers.read')
    def get_customer(self, customer_id):
        """Get customer details - requires read permission"""
        return self.customer_service.get(customer_id)

    @require_permission('customers.update')
    def update_customer(self, customer_id, updates):
        """Update customer - requires update permission"""
        return self.customer_service.update(customer_id, updates)

    @require_permission('customers.delete')
    def delete_customer(self, customer_id):
        """Delete customer - requires delete permission"""
        return self.customer_service.delete(customer_id)
```

### Language Selection System

#### Multi-Language Support Implementation
```python
class LanguageService:
    def __init__(self):
        self.translations = {}
        self.current_language = 'en'
        self.load_translations()

    def load_translations(self):
        """Load translation files"""
        import json
        import os

        lang_dir = 'resources/languages'
        for filename in os.listdir(lang_dir):
            if filename.endswith('.json'):
                lang_code = filename[:-5]  # Remove .json
                with open(os.path.join(lang_dir, filename), 'r', encoding='utf-8') as f:
                    self.translations[lang_code] = json.load(f)

    def set_language(self, language_code):
        """Set current language"""
        if language_code in self.translations:
            self.current_language = language_code
            return True
        return False

    def translate(self, key, **kwargs):
        """Translate text key to current language"""
        keys = key.split('.')
        translation = self.translations.get(self.current_language, {})

        for k in keys:
            if isinstance(translation, dict) and k in translation:
                translation = translation[k]
            else:
                # Fallback to English if translation not found
                translation = self.translations.get('en', {})
                for k in keys:
                    if isinstance(translation, dict) and k in translation:
                        translation = translation[k]
                    else:
                        return key  # Return key if no translation found
                break

        # Handle string formatting
        if isinstance(translation, str) and kwargs:
            return translation.format(**kwargs)

        return translation

    def get_direction(self):
        """Get text direction for current language"""
        rtl_languages = ['ar', 'he', 'fa', 'ur']
        return 'rtl' if self.current_language in rtl_languages else 'ltr'
```

#### UI Language Switching
```python
class LanguageAwareWidget:
    def __init__(self):
        self.language_service = LanguageService()
        self.widgets = {}

    def update_language(self, language_code):
        """Update all UI elements to new language"""
        self.language_service.set_language(language_code)

        # Update text direction
        direction = self.language_service.get_direction()
        self.configure_text_direction(direction)

        # Update all translatable widgets
        self.refresh_translations()

    def configure_text_direction(self, direction):
        """Configure UI for RTL/LTR text direction"""
        if direction == 'rtl':
            # Configure RTL layout
            self.configure(justify='right')
            # Adjust padding and alignment
        else:
            # Configure LTR layout
            self.configure(justify='left')

    def refresh_translations(self):
        """Refresh all translatable text"""
        for widget_id, translation_key in self.widgets.items():
            widget = self.get_widget(widget_id)
            if widget:
                translated_text = self.language_service.translate(translation_key)
                widget.configure(text=translated_text)

## Implementation Guidelines

### Development Standards

#### Code Organization
```python
# Base classes for consistent structure
class BaseModel:
    """Base model class with common functionality"""
    def __init__(self):
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

    def save(self):
        """Save model to database"""
        self.updated_at = datetime.now()
        # Implementation here

    def delete(self):
        """Soft delete model"""
        self.deleted_at = datetime.now()
        # Implementation here

class BaseController:
    """Base controller with common CRUD operations"""
    def __init__(self, model_class, service_class):
        self.model = model_class
        self.service = service_class()

    def create(self, data):
        """Create new record"""
        # Validation and creation logic
        pass

    def read(self, id):
        """Read record by ID"""
        pass

    def update(self, id, data):
        """Update existing record"""
        pass

    def delete(self, id):
        """Delete record"""
        pass

class BaseView:
    """Base view class for UI components"""
    def __init__(self, parent, controller):
        self.parent = parent
        self.controller = controller
        self.language_service = LanguageService()
        self.setup_ui()

    def setup_ui(self):
        """Setup UI components"""
        pass

    def refresh_data(self):
        """Refresh view data"""
        pass
```

#### Coding Standards
1. **PEP 8 Compliance**: Follow Python PEP 8 style guide
2. **Type Hints**: Use type hints for better code documentation
3. **Docstrings**: Comprehensive docstrings for all classes and methods
4. **Error Handling**: Proper exception handling with custom exceptions
5. **Logging**: Structured logging throughout the application
6. **Testing**: Unit tests for all business logic

#### Configuration Management
```python
# config/settings.py
import os
from dataclasses import dataclass

@dataclass
class DatabaseConfig:
    path: str = "data/management.db"
    backup_path: str = "backups/"
    auto_backup: bool = True
    backup_interval: int = 24  # hours

@dataclass
class SecurityConfig:
    session_timeout: int = 120  # minutes
    max_login_attempts: int = 5
    lockout_duration: int = 30  # minutes
    password_min_length: int = 8
    require_special_chars: bool = True

@dataclass
class UIConfig:
    theme: str = "light"
    default_language: str = "en"
    window_width: int = 1200
    window_height: int = 800
    auto_save_interval: int = 300  # seconds

@dataclass
class AppConfig:
    debug: bool = False
    log_level: str = "INFO"
    database: DatabaseConfig = DatabaseConfig()
    security: SecurityConfig = SecurityConfig()
    ui: UIConfig = UIConfig()

# Load configuration from environment or config file
def load_config():
    config = AppConfig()

    # Override with environment variables
    if os.getenv('DEBUG'):
        config.debug = os.getenv('DEBUG').lower() == 'true'

    if os.getenv('DATABASE_PATH'):
        config.database.path = os.getenv('DATABASE_PATH')

    return config
```

#### Error Handling Strategy
```python
# Custom exceptions
class ManagementSystemError(Exception):
    """Base exception for management system"""
    pass

class ValidationError(ManagementSystemError):
    """Raised when data validation fails"""
    pass

class PermissionError(ManagementSystemError):
    """Raised when user lacks required permissions"""
    pass

class BusinessLogicError(ManagementSystemError):
    """Raised when business rules are violated"""
    pass

# Error handler decorator
def handle_errors(func):
    """Decorator to handle common errors"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValidationError as e:
            logger.warning(f"Validation error in {func.__name__}: {e}")
            show_error_message("Validation Error", str(e))
        except PermissionError as e:
            logger.warning(f"Permission error in {func.__name__}: {e}")
            show_error_message("Access Denied", str(e))
        except BusinessLogicError as e:
            logger.error(f"Business logic error in {func.__name__}: {e}")
            show_error_message("Business Rule Violation", str(e))
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {e}")
            show_error_message("System Error", "An unexpected error occurred")
    return wrapper
```

#### Logging Configuration
```python
# utils/logging_config.py
import logging
import logging.handlers
import os

def setup_logging(log_level="INFO"):
    """Setup application logging"""

    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level))

    # Console handler
    console_handler = logging.StreamHandler()
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        'logs/management_system.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    # Audit log handler
    audit_handler = logging.handlers.RotatingFileHandler(
        'logs/audit.log',
        maxBytes=10*1024*1024,
        backupCount=10
    )
    audit_formatter = logging.Formatter(
        '%(asctime)s - %(message)s'
    )
    audit_handler.setFormatter(audit_formatter)

    # Create audit logger
    audit_logger = logging.getLogger('audit')
    audit_logger.addHandler(audit_handler)
    audit_logger.setLevel(logging.INFO)
```

### Database Management

#### Database Initialization
```python
# services/database_service.py
import sqlite3
import os
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker

class DatabaseService:
    def __init__(self, db_path="data/management.db"):
        self.db_path = db_path
        self.engine = None
        self.Session = None
        self.initialize_database()

    def initialize_database(self):
        """Initialize database connection and create tables"""
        # Ensure data directory exists
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        # Create SQLAlchemy engine
        self.engine = create_engine(f'sqlite:///{self.db_path}')
        self.Session = sessionmaker(bind=self.engine)

        # Create tables if they don't exist
        self.create_tables()

        # Insert default data
        self.insert_default_data()

    def create_tables(self):
        """Create all database tables"""
        # Import all models to register them
        from models import *

        # Create all tables
        Base.metadata.create_all(self.engine)

    def insert_default_data(self):
        """Insert default system data"""
        session = self.Session()
        try:
            # Insert default roles and permissions
            self.insert_default_roles(session)
            self.insert_default_permissions(session)
            self.create_admin_user(session)

            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def backup_database(self, backup_path=None):
        """Create database backup"""
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backups/backup_{timestamp}.db"

        os.makedirs(os.path.dirname(backup_path), exist_ok=True)

        # Copy database file
        import shutil
        shutil.copy2(self.db_path, backup_path)

        return backup_path

    def restore_database(self, backup_path):
        """Restore database from backup"""
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"Backup file not found: {backup_path}")

        # Close current connections
        self.engine.dispose()

        # Replace database file
        import shutil
        shutil.copy2(backup_path, self.db_path)

        # Reinitialize connection
        self.initialize_database()
```

#### Data Validation
```python
# utils/validators.py
import re
from datetime import datetime

class Validator:
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    @staticmethod
    def validate_phone(phone):
        """Validate phone number format"""
        pattern = r'^\+?[\d\s\-\(\)]{10,}$'
        return re.match(pattern, phone) is not None

    @staticmethod
    def validate_required(value, field_name):
        """Validate required field"""
        if not value or (isinstance(value, str) and not value.strip()):
            raise ValidationError(f"{field_name} is required")

    @staticmethod
    def validate_length(value, min_length=None, max_length=None, field_name="Field"):
        """Validate string length"""
        if min_length and len(value) < min_length:
            raise ValidationError(f"{field_name} must be at least {min_length} characters")

        if max_length and len(value) > max_length:
            raise ValidationError(f"{field_name} must not exceed {max_length} characters")

    @staticmethod
    def validate_decimal(value, min_value=None, max_value=None, field_name="Value"):
        """Validate decimal value"""
        try:
            decimal_value = float(value)
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name} must be a valid number")

        if min_value is not None and decimal_value < min_value:
            raise ValidationError(f"{field_name} must be at least {min_value}")

        if max_value is not None and decimal_value > max_value:
            raise ValidationError(f"{field_name} must not exceed {max_value}")

        return decimal_value

## Scalability & Maintenance

### Performance Optimization

#### Database Optimization
1. **Indexing Strategy**
   - Primary keys on all tables
   - Foreign key indexes for joins
   - Composite indexes for common queries
   - Full-text search indexes for text fields

2. **Query Optimization**
   - Use prepared statements
   - Implement query result caching
   - Optimize N+1 query problems
   - Use database views for complex reports

3. **Connection Management**
   - Connection pooling
   - Proper connection cleanup
   - Transaction management
   - Read/write connection separation

#### Application Performance
```python
# Caching implementation
from functools import lru_cache
import redis

class CacheService:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        self.local_cache = {}

    @lru_cache(maxsize=1000)
    def get_user_permissions(self, user_id):
        """Cache user permissions"""
        # Implementation here
        pass

    def cache_report_data(self, report_key, data, expiry=3600):
        """Cache report data with expiry"""
        self.redis_client.setex(report_key, expiry, json.dumps(data))

    def get_cached_report(self, report_key):
        """Get cached report data"""
        cached_data = self.redis_client.get(report_key)
        return json.loads(cached_data) if cached_data else None
```

### Scalability Considerations

#### Horizontal Scaling
1. **Database Scaling**
   - Master-slave replication for read scaling
   - Database sharding for large datasets
   - Separate databases for different modules
   - Archive old data to separate storage

2. **Application Scaling**
   - Microservices architecture for large deployments
   - API-first design for multiple clients
   - Load balancing for multiple instances
   - Stateless application design

#### Vertical Scaling
1. **Resource Optimization**
   - Memory usage optimization
   - CPU-intensive task optimization
   - Disk I/O optimization
   - Network bandwidth optimization

### Maintenance Strategy

#### Backup and Recovery
```python
class BackupService:
    def __init__(self):
        self.backup_schedule = {
            'daily': True,
            'weekly': True,
            'monthly': True
        }

    def create_full_backup(self):
        """Create complete system backup"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Database backup
        db_backup = self.backup_database(f"backups/db_backup_{timestamp}.db")

        # File system backup
        files_backup = self.backup_files(f"backups/files_backup_{timestamp}.zip")

        # Configuration backup
        config_backup = self.backup_configuration(f"backups/config_backup_{timestamp}.json")

        return {
            'database': db_backup,
            'files': files_backup,
            'configuration': config_backup,
            'timestamp': timestamp
        }

    def schedule_backups(self):
        """Schedule automatic backups"""
        import schedule

        if self.backup_schedule['daily']:
            schedule.every().day.at("02:00").do(self.create_full_backup)

        if self.backup_schedule['weekly']:
            schedule.every().sunday.at("01:00").do(self.create_full_backup)

        if self.backup_schedule['monthly']:
            schedule.every().month.do(self.create_full_backup)
```

#### Update and Migration System
```python
class MigrationService:
    def __init__(self):
        self.current_version = self.get_current_version()
        self.migrations_path = "migrations/"

    def run_migrations(self, target_version=None):
        """Run database migrations"""
        available_migrations = self.get_available_migrations()

        for migration in available_migrations:
            if migration.version > self.current_version:
                if target_version and migration.version > target_version:
                    break

                self.run_migration(migration)
                self.update_version(migration.version)

    def create_migration(self, description):
        """Create new migration file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{description}.py"

        migration_template = '''
"""
Migration: {description}
Created: {timestamp}
"""

def upgrade(connection):
    """Apply migration"""
    # Add upgrade logic here
    pass

def downgrade(connection):
    """Rollback migration"""
    # Add rollback logic here
    pass
'''

        with open(f"{self.migrations_path}/{filename}", 'w') as f:
            f.write(migration_template.format(
                description=description,
                timestamp=timestamp
            ))
```

#### Monitoring and Logging
```python
class MonitoringService:
    def __init__(self):
        self.metrics = {}
        self.alerts = []

    def track_performance(self, operation, duration):
        """Track operation performance"""
        if operation not in self.metrics:
            self.metrics[operation] = []

        self.metrics[operation].append({
            'duration': duration,
            'timestamp': datetime.now()
        })

        # Check for performance issues
        self.check_performance_alerts(operation, duration)

    def check_system_health(self):
        """Check overall system health"""
        health_status = {
            'database': self.check_database_health(),
            'disk_space': self.check_disk_space(),
            'memory_usage': self.check_memory_usage(),
            'active_sessions': self.count_active_sessions()
        }

        return health_status

    def generate_health_report(self):
        """Generate system health report"""
        health = self.check_system_health()

        report = {
            'timestamp': datetime.now(),
            'status': 'healthy' if all(health.values()) else 'warning',
            'details': health,
            'recommendations': self.get_recommendations(health)
        }

        return report
```

### Future Enhancement Roadmap

#### Phase 1: Core System (Months 1-3)
- Basic CRUD operations for all modules
- User authentication and authorization
- Basic reporting functionality
- Multi-language support

#### Phase 2: Advanced Features (Months 4-6)
- Advanced reporting and analytics
- Document management system
- API development for integrations
- Mobile-responsive interface

#### Phase 3: Enterprise Features (Months 7-9)
- Multi-company support
- Advanced workflow management
- Integration with external systems
- Advanced security features

#### Phase 4: AI and Analytics (Months 10-12)
- Predictive analytics
- AI-powered insights
- Machine learning for forecasting
- Advanced business intelligence

### Deployment Strategy

#### Development Environment
```bash
# Setup development environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# Initialize database
python scripts/init_database.py

# Run application
python main.py
```

#### Production Deployment
```bash
# Production setup
pip install -r requirements-prod.txt

# Environment configuration
export ENVIRONMENT=production
export DATABASE_PATH=/opt/management_system/data/production.db
export LOG_LEVEL=WARNING

# Run with production settings
python main.py --config production.ini
```

#### Docker Deployment
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "main.py"]
```

## Conclusion

This comprehensive management system design provides a solid foundation for building a professional business management application. The modular architecture ensures maintainability and scalability, while the detailed specifications provide clear guidance for implementation.

Key strengths of this design:
- **Comprehensive Coverage**: All essential business modules included
- **Scalable Architecture**: Designed for growth and expansion
- **Security Focus**: Robust authentication and authorization
- **User Experience**: Intuitive interface with multi-language support
- **Maintainability**: Clean code structure and documentation
- **Performance**: Optimized for efficiency and responsiveness

The system can be implemented incrementally, starting with core modules and gradually adding advanced features. The flexible design allows for customization based on specific business requirements while maintaining the overall system integrity.
```
```
