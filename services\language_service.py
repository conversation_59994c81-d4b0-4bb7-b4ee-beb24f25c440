"""
Language service for multi-language support
"""
import json
import os
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class LanguageService:
    """Service for handling multi-language support"""
    
    def __init__(self):
        self.translations = {}
        self.current_language = 'en'
        self.supported_languages = {
            'en': {
                'name': 'English',
                'direction': 'ltr',
                'font': 'Segoe UI'
            },
            'ar': {
                'name': 'العربية',
                'direction': 'rtl',
                'font': 'Tahoma'
            }
        }
        self.load_translations()
    
    def load_translations(self):
        """Load translation files"""
        try:
            # Create default translations if files don't exist
            self.create_default_translations()
            
            # Load translation files
            lang_dir = Path('resources/languages')
            if lang_dir.exists():
                for filename in lang_dir.glob('*.json'):
                    lang_code = filename.stem
                    try:
                        with open(filename, 'r', encoding='utf-8') as f:
                            self.translations[lang_code] = json.load(f)
                        logger.info(f"Loaded translations for {lang_code}")
                    except Exception as e:
                        logger.error(f"Failed to load translations for {lang_code}: {e}")
            
            # Ensure we have at least English translations
            if 'en' not in self.translations:
                self.translations['en'] = self.get_default_english_translations()
            
            if 'ar' not in self.translations:
                self.translations['ar'] = self.get_default_arabic_translations()
                
        except Exception as e:
            logger.error(f"Failed to load translations: {e}")
            # Fallback to basic translations
            self.translations = {
                'en': self.get_default_english_translations(),
                'ar': self.get_default_arabic_translations()
            }
    
    def create_default_translations(self):
        """Create default translation files"""
        try:
            lang_dir = Path('resources/languages')
            lang_dir.mkdir(parents=True, exist_ok=True)
            
            # Create English translations
            en_file = lang_dir / 'en.json'
            if not en_file.exists():
                with open(en_file, 'w', encoding='utf-8') as f:
                    json.dump(self.get_default_english_translations(), f, indent=2, ensure_ascii=False)
            
            # Create Arabic translations
            ar_file = lang_dir / 'ar.json'
            if not ar_file.exists():
                with open(ar_file, 'w', encoding='utf-8') as f:
                    json.dump(self.get_default_arabic_translations(), f, indent=2, ensure_ascii=False)
                    
        except Exception as e:
            logger.error(f"Failed to create default translations: {e}")
    
    def get_default_english_translations(self):
        """Get default English translations"""
        return {
            "app": {
                "title": "Management System",
                "version": "v1.0"
            },
            "login": {
                "title": "Management System Login",
                "username": "Username",
                "password": "Password",
                "login_button": "Login",
                "forgot_password": "Forgot Password?",
                "language": "Language",
                "welcome": "Welcome to Management System",
                "invalid_credentials": "Invalid username or password",
                "login_success": "Login successful"
            },
            "dashboard": {
                "title": "Dashboard",
                "welcome": "Welcome",
                "customers": "Customers",
                "products": "Products",
                "sales": "Sales",
                "suppliers": "Suppliers",
                "orders": "Orders",
                "payments": "Payments",
                "recent_activities": "Recent Activities",
                "quick_actions": "Quick Actions"
            },
            "menu": {
                "dashboard": "Dashboard",
                "customers": "Customer Management",
                "inventory": "Inventory Management",
                "sales": "Sales Management",
                "purchases": "Purchase Management",
                "suppliers": "Supplier Management",
                "employees": "Employee Management",
                "finance": "Finance Management",
                "invoices": "Invoice Management",
                "reports": "Reports",
                "documents": "Document Management",
                "settings": "System Settings",
                "logout": "Logout"
            },
            "common": {
                "save": "Save",
                "cancel": "Cancel",
                "delete": "Delete",
                "edit": "Edit",
                "view": "View",
                "add": "Add",
                "search": "Search",
                "filter": "Filter",
                "export": "Export",
                "print": "Print",
                "close": "Close",
                "yes": "Yes",
                "no": "No",
                "ok": "OK",
                "error": "Error",
                "success": "Success",
                "warning": "Warning",
                "info": "Information"
            }
        }
    
    def get_default_arabic_translations(self):
        """Get default Arabic translations"""
        return {
            "app": {
                "title": "نظام الإدارة",
                "version": "الإصدار 1.0"
            },
            "login": {
                "title": "تسجيل دخول نظام الإدارة",
                "username": "اسم المستخدم",
                "password": "كلمة المرور",
                "login_button": "تسجيل الدخول",
                "forgot_password": "نسيت كلمة المرور؟",
                "language": "اللغة",
                "welcome": "مرحباً بك في نظام الإدارة",
                "invalid_credentials": "اسم المستخدم أو كلمة المرور غير صحيحة",
                "login_success": "تم تسجيل الدخول بنجاح"
            },
            "dashboard": {
                "title": "لوحة التحكم",
                "welcome": "مرحباً",
                "customers": "العملاء",
                "products": "المنتجات",
                "sales": "المبيعات",
                "suppliers": "الموردين",
                "orders": "الطلبات",
                "payments": "المدفوعات",
                "recent_activities": "الأنشطة الحديثة",
                "quick_actions": "إجراءات سريعة"
            },
            "menu": {
                "dashboard": "لوحة التحكم",
                "customers": "إدارة العملاء",
                "inventory": "إدارة المخزون",
                "sales": "إدارة المبيعات",
                "purchases": "إدارة المشتريات",
                "suppliers": "إدارة الموردين",
                "employees": "إدارة الموظفين",
                "finance": "الإدارة المالية",
                "invoices": "إدارة الفواتير",
                "reports": "التقارير",
                "documents": "إدارة المستندات",
                "settings": "إعدادات النظام",
                "logout": "تسجيل الخروج"
            },
            "common": {
                "save": "حفظ",
                "cancel": "إلغاء",
                "delete": "حذف",
                "edit": "تعديل",
                "view": "عرض",
                "add": "إضافة",
                "search": "بحث",
                "filter": "تصفية",
                "export": "تصدير",
                "print": "طباعة",
                "close": "إغلاق",
                "yes": "نعم",
                "no": "لا",
                "ok": "موافق",
                "error": "خطأ",
                "success": "نجح",
                "warning": "تحذير",
                "info": "معلومات"
            }
        }
    
    def set_language(self, language_code):
        """Set current language"""
        if language_code in self.supported_languages:
            self.current_language = language_code
            logger.info(f"Language set to: {language_code}")
            return True
        else:
            logger.warning(f"Unsupported language: {language_code}")
            return False
    
    def translate(self, key, **kwargs):
        """Translate text key to current language"""
        keys = key.split('.')
        translation = self.translations.get(self.current_language, {})
        
        # Navigate through nested keys
        for k in keys:
            if isinstance(translation, dict) and k in translation:
                translation = translation[k]
            else:
                # Fallback to English if translation not found
                translation = self.translations.get('en', {})
                for k in keys:
                    if isinstance(translation, dict) and k in translation:
                        translation = translation[k]
                    else:
                        return key  # Return key if no translation found
                break
        
        # Handle string formatting
        if isinstance(translation, str) and kwargs:
            try:
                return translation.format(**kwargs)
            except KeyError:
                return translation
        
        return translation if isinstance(translation, str) else key
    
    def get_direction(self):
        """Get text direction for current language"""
        return self.supported_languages.get(self.current_language, {}).get('direction', 'ltr')
    
    def get_font(self):
        """Get recommended font for current language"""
        return self.supported_languages.get(self.current_language, {}).get('font', 'Segoe UI')
    
    def get_language_name(self, language_code=None):
        """Get language display name"""
        if language_code is None:
            language_code = self.current_language
        return self.supported_languages.get(language_code, {}).get('name', language_code)
    
    def get_supported_languages(self):
        """Get list of supported languages"""
        return list(self.supported_languages.keys())
