# 🏢 نظام الإدارة الاحترافي - Professional Management System

نظام إدارة أعمال شامل مبني بـ Python و SQLite3، يتميز بواجهة مستخدم احترافية حديثة مع دعم متعدد اللغات (العربية/الإنجليزية).

A comprehensive business management system built with Python and SQLite3, featuring a **professional modern GUI interface** with multi-language support (Arabic/English).

## 🎨 **الواجهة الاحترافية الجديدة - New Professional UI**

### ✨ **المميزات البصرية الجديدة**
- 🎨 **تصميم احترافي حديث** مع CustomTkinter
- 🌈 **نظام ألوان متناسق** ومريح للعين
- 🖼️ **أيقونات تعبيرية** واضحة ومفهومة
- 💫 **تأثيرات بصرية جذابة** مع انتقالات سلسة
- 📱 **تصميم متجاوب** يتكيف مع أحجام الشاشات المختلفة

### 🖥️ **لقطات من الواجهة الجديدة - UI Screenshots**

#### 🔐 شاشة تسجيل الدخول الاحترافية
```
┌─────────────────────────────────────────┐
│           🏢 نظام الإدارة              │
│        مرحباً بك في نظام الإدارة       │
│                                         │
│  🌐 اللغة: [العربية ▼]                │
│                                         │
│  👤 اسم المستخدم                      │
│  [________________________]            │
│                                         │
│  🔒 كلمة المرور                        │
│  [________________________]            │
│                                         │
│        🚀 تسجيل الدخول                 │
│                                         │
│         نسيت كلمة المرور؟               │
└─────────────────────────────────────────┘
```

#### 🏠 لوحة التحكم الاحترافية
```
┌─────────────────────────────────────────────────────────────┐
│ الشريط الجانبي    │           المحتوى الرئيسي              │
│                   │                                         │
│ 👤 المدير         │  مرحباً، المدير! 👋                   │
│ مدير النظام       │                                         │
│                   │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐       │
│ 🏠 لوحة التحكم    │  │👥 العملاء│📦 المنتجات│💰 المبيعات│📋 الطلبات│ │
│ 👥 إدارة العملاء  │  │ 1,234 │ 567  │$12,345│  89  │       │
│ 📦 إدارة المخزون  │  │ +12%  │ +5%  │ +18%  │ -3%  │       │
│ 💰 إدارة المبيعات │  └─────┘ └─────┘ └─────┘ └─────┘       │
│ 🛒 إدارة المشتريات│                                         │
│ 🏪 إدارة الموردين │  📋 الأنشطة الحديثة                   │
│ 👨‍💼 إدارة الموظفين │  • عميل جديد: شركة ABC                │
│ 💳 الإدارة المالية│  • طلب بيع #1234 بقيمة $5,000        │
│ 🧾 إدارة الفواتير │  • دفعة مستلمة: $2,500               │
│ 📊 التقارير       │                                         │
│ 📁 إدارة المستندات│                                         │
│ ⚙️ الإعدادات      │                                         │
│                   │                                         │
│ 🚪 تسجيل الخروج   │                                         │
└─────────────────────────────────────────────────────────────┘
```

## 🌟 Features - الميزات

### 🏗️ Core Modules - الوحدات الأساسية
- **Customer Management** - Complete customer relationship management
- **Inventory Management** - Product catalog and stock tracking
- **Sales Management** - Sales orders and quotations
- **Purchase Management** - Procurement and supplier management
- **Supplier Management** - Vendor relationship management
- **Employee Management** - HR and staff management
- **Finance Management** - Accounting and financial tracking
- **Invoice Management** - Billing and payment processing
- **Reports Management** - Business analytics and reporting
- **Document Management** - File storage and archiving

### 🔑 Key Features - الميزات الرئيسية
- 🌐 **Multi-language Support** - دعم اللغة العربية والإنجليزية مع RTL/LTR
- 🔐 **Role-based Access Control** - نظام أذونات دقيق ومرن
- 🎨 **Professional Modern UI** - واجهة احترافية حديثة مع CustomTkinter
- 📊 **Real-time Dashboard** - لوحة تحكم فورية مع مخططات وإحصائيات
- 📈 **Comprehensive Reporting** - تقارير شاملة وقابلة للتخصيص
- 📁 **Document Management** - إدارة وأرشفة المستندات
- 💾 **Database Management** - نسخ احتياطي واستعادة وصيانة قاعدة البيانات
- ✨ **Professional Dialogs** - حوارات مخصصة وجذابة
- 🎭 **Smooth Animations** - انتقالات وتأثيرات بصرية سلسة
- 🃏 **Modern Cards Design** - تصميم بطاقات عصري مع ظلال

## 🛠️ Technology Stack - المكدس التقني

- 🐍 **Python 3.9+** - لغة البرمجة الأساسية
- 🗄️ **SQLite3** - محرك قاعدة البيانات
- 🎨 **CustomTkinter** - إطار عمل واجهة المستخدم الحديث
- 🔗 **SQLAlchemy** - ORM قاعدة البيانات (اختياري)
- 📊 **Matplotlib** - الرسوم البيانية والمخططات (اختياري)
- 📄 **ReportLab** - إنتاج تقارير PDF (اختياري)
- 🖼️ **Pillow** - معالجة الصور
- 🔒 **bcrypt** - تشفير كلمات المرور

### 🎨 **UI Framework الجديد**
- **CustomTkinter 5.2+** - واجهة حديثة وجذابة
- **Professional Color Scheme** - نظام ألوان احترافي
- **Responsive Design** - تصميم متجاوب
- **Custom Dialogs** - حوارات مخصصة

## Installation

### Prerequisites
- Python 3.9 or higher
- pip (Python package installer)

### Setup Instructions

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd management-system
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   
   # On Windows
   venv\Scripts\activate
   
   # On macOS/Linux
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Initialize database**
   ```bash
   python scripts/init_database.py
   ```

5. **Run the application**
   ```bash
   python main.py
   ```

## Default Login Credentials

- **Username:** admin
- **Password:** admin123

⚠️ **Important:** Change the default password immediately after first login.

## Project Structure

```
management_system/
├── main.py                 # Application entry point
├── config/                 # Configuration files
├── models/                 # Database models
├── views/                  # UI components
├── controllers/            # Business logic
├── services/               # Core services
├── utils/                  # Utility functions
├── resources/              # Icons, images, translations
├── data/                   # Database files
├── reports/                # Generated reports
├── uploads/                # Uploaded documents
├── backups/                # Database backups
└── tests/                  # Unit tests
```

## Configuration

### Database Configuration
Edit `config/settings.py` to customize database settings:

```python
@dataclass
class DatabaseConfig:
    path: str = "data/management.db"
    backup_path: str = "backups/"
    auto_backup: bool = True
    backup_interval: int = 24  # hours
```

### UI Configuration
Customize the user interface:

```python
@dataclass
class UIConfig:
    theme: str = "light"
    default_language: str = "en"
    window_width: int = 1200
    window_height: int = 800
```

## Usage Guide

### First Time Setup

1. **Login** with default credentials
2. **Change password** in user settings
3. **Configure company information** in system settings
4. **Create user roles** and assign permissions
5. **Add users** for your team
6. **Setup product categories** and initial inventory
7. **Configure customers and suppliers**

### Daily Operations

1. **Dashboard** - Monitor key metrics and recent activities
2. **Sales** - Create orders, generate invoices, track payments
3. **Purchases** - Manage procurement and supplier relationships
4. **Inventory** - Track stock levels and movements
5. **Reports** - Generate business analytics and insights

## Language Support

The system supports multiple languages with automatic text direction handling:

- **English (en)** - Left-to-right (LTR)
- **Arabic (ar)** - Right-to-left (RTL)

### Adding New Languages

1. Create translation file in `resources/languages/`
2. Add language configuration in `services/language_service.py`
3. Update UI components to use translation keys

## Security Features

- **Password Hashing** - bcrypt with salt
- **Session Management** - Secure token-based sessions
- **Role-based Access** - Granular permission system
- **Account Lockout** - Protection against brute force attacks
- **Audit Logging** - Complete activity tracking

## Backup and Recovery

### Automatic Backups
The system automatically creates daily backups of:
- Database files
- Configuration settings
- Uploaded documents

### Manual Backup
```bash
python scripts/backup_system.py
```

### Restore from Backup
```bash
python scripts/restore_system.py --backup-file backup_20240101_120000.zip
```

## Development

### Running Tests
```bash
python -m pytest tests/
```

### Code Formatting
```bash
black .
flake8 .
```

### Type Checking
```bash
mypy .
```

## Troubleshooting

### Common Issues

1. **Database locked error**
   - Close all application instances
   - Check for zombie processes
   - Restart the application

2. **Permission denied errors**
   - Check user roles and permissions
   - Verify database file permissions
   - Run as administrator if needed

3. **UI display issues**
   - Update graphics drivers
   - Check display scaling settings
   - Try different themes

### Log Files
Check log files for detailed error information:
- `logs/management_system.log` - Application logs
- `logs/audit.log` - User activity logs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the documentation
- Review log files for errors
- Create an issue in the repository
- Contact the development team

## Roadmap

### Version 1.1 (Planned)
- Mobile app companion
- Advanced reporting features
- API for third-party integrations
- Multi-company support

### Version 1.2 (Planned)
- Cloud deployment options
- Advanced workflow automation
- AI-powered insights
- Enhanced security features

---

**Management System v1.0** - Professional Business Management Solution
