#!/usr/bin/env python3
"""
Setup script for Management System
This script helps with initial setup and dependency installation
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    essential_packages = [
        "customtkinter>=5.0.0",
        "pillow>=9.0.0", 
        "bcrypt>=4.0.0"
    ]
    
    for package in essential_packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    return True

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        "data",
        "logs", 
        "backups",
        "uploads",
        "reports",
        "resources/icons",
        "resources/images",
        "resources/themes"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def check_database():
    """Check if database exists and is accessible"""
    print("\n🗄️ Checking database...")
    
    db_path = Path("data/management.db")
    if db_path.exists():
        print(f"✅ Database found: {db_path}")
    else:
        print("ℹ️ Database will be created on first run")
    
    return True

def main():
    """Main setup function"""
    print("🚀 Management System Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Create directories
    create_directories()
    
    # Check database
    check_database()
    
    print("\n✅ Setup completed successfully!")
    print("\nTo run the application:")
    print("  Windows: run.bat")
    print("  Or: python main.py")
    print("\nDefault login credentials:")
    print("  Username: admin")
    print("  Password: admin123")
    print("\n⚠️ Remember to change the default password after first login!")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)
