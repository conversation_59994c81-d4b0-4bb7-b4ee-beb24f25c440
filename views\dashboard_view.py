"""
Dashboard view for the Management System - Professional UI
"""
import customtkinter as ctk
from tkinter import messagebox
import logging
from PIL import Image, ImageTk
# Optional imports - will be used when charts are implemented
# import matplotlib.pyplot as plt
# from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
# import numpy as np

logger = logging.getLogger(__name__)

# Set appearance mode and color theme
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

class DashboardView:
    """Professional main dashboard window"""

    def __init__(self, app):
        self.app = app
        self.root = None

        # Professional color scheme
        self.colors = {
            'primary': '#2E86AB',      # Professional Blue
            'secondary': '#A23B72',    # Accent Purple
            'success': '#28A745',      # Success Green
            'warning': '#FFC107',      # Warning Yellow
            'danger': '#DC3545',       # Danger Red
            'light': '#F8F9FA',        # Light Gray
            'dark': '#343A40',         # Dark Gray
            'white': '#FFFFFF',        # Pure White
            'card_bg': '#FFFFFF',      # Card Background
            'sidebar_bg': '#2C3E50',   # Sidebar Background
            'gradient_start': '#667eea',
            'gradient_end': '#764ba2'
        }

        self.create_dashboard()
    
    def create_dashboard(self):
        """Create the professional main dashboard window"""
        self.root = ctk.CTk()
        self.root.title(f"{self.app.language_service.translate('app.title')} - {self.app.language_service.translate('dashboard.title')}")
        self.root.geometry("1400x900")
        self.root.state('zoomed')  # Maximize window on Windows

        # Set window icon
        try:
            self.root.iconbitmap("resources/icons/app_icon.ico")
        except:
            pass

        # Create main layout with sidebar
        self.create_main_layout()

        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
    
    def create_main_layout(self):
        """Create the main layout with sidebar and content area"""
        # Main container
        main_container = ctk.CTkFrame(self.root, corner_radius=0, fg_color="transparent")
        main_container.pack(fill="both", expand=True)

        # Create sidebar
        self.create_sidebar(main_container)

        # Create main content area
        self.create_content_area(main_container)

    def create_sidebar(self, parent):
        """Create the professional sidebar"""
        sidebar = ctk.CTkFrame(
            parent,
            width=280,
            corner_radius=0,
            fg_color=self.colors['sidebar_bg']
        )
        sidebar.pack(side="left", fill="y")
        sidebar.pack_propagate(False)

        # Sidebar header with user info
        header_frame = ctk.CTkFrame(sidebar, fg_color="transparent")
        header_frame.pack(fill="x", padx=20, pady=(20, 30))

        # User avatar placeholder
        avatar_frame = ctk.CTkFrame(
            header_frame,
            width=60,
            height=60,
            corner_radius=30,
            fg_color=self.colors['primary']
        )
        avatar_frame.pack(pady=(0, 15))

        avatar_label = ctk.CTkLabel(
            avatar_frame,
            text="👤",
            font=ctk.CTkFont(size=30),
            text_color=self.colors['white']
        )
        avatar_label.pack(expand=True)

        # User name and role
        user_name = ctk.CTkLabel(
            header_frame,
            text=f"{self.app.current_user.first_name} {self.app.current_user.last_name}",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors['white']
        )
        user_name.pack()

        user_role = ctk.CTkLabel(
            header_frame,
            text="System Administrator",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['light']
        )
        user_role.pack(pady=(5, 0))

        # Navigation menu
        self.create_navigation_menu(sidebar)
    
    def create_navigation_menu(self, parent):
        """Create the navigation menu in sidebar"""
        menu_frame = ctk.CTkScrollableFrame(
            parent,
            fg_color="transparent"
        )
        menu_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Menu items with icons
        menu_items = [
            ("🏠", self.app.language_service.translate("menu.dashboard"), self.show_dashboard),
            ("👥", self.app.language_service.translate("menu.customers"), self.placeholder_command),
            ("📦", self.app.language_service.translate("menu.inventory"), self.placeholder_command),
            ("💰", self.app.language_service.translate("menu.sales"), self.placeholder_command),
            ("🛒", self.app.language_service.translate("menu.purchases"), self.placeholder_command),
            ("🏪", self.app.language_service.translate("menu.suppliers"), self.placeholder_command),
            ("👨‍💼", self.app.language_service.translate("menu.employees"), self.placeholder_command),
            ("💳", self.app.language_service.translate("menu.finance"), self.placeholder_command),
            ("🧾", self.app.language_service.translate("menu.invoices"), self.placeholder_command),
            ("📊", self.app.language_service.translate("menu.reports"), self.placeholder_command),
            ("📁", self.app.language_service.translate("menu.documents"), self.placeholder_command),
            ("⚙️", self.app.language_service.translate("menu.settings"), self.placeholder_command),
        ]

        self.menu_buttons = []
        for icon, text, command in menu_items:
            btn = ctk.CTkButton(
                menu_frame,
                text=f"{icon}  {text}",
                command=command,
                height=45,
                font=ctk.CTkFont(size=14),
                fg_color="transparent",
                text_color=self.colors['white'],
                hover_color=self.colors['primary'],
                anchor="w",
                corner_radius=10
            )
            btn.pack(fill="x", pady=2)
            self.menu_buttons.append(btn)

        # Logout button at bottom
        logout_btn = ctk.CTkButton(
            parent,
            text="🚪  " + self.app.language_service.translate("menu.logout"),
            command=self.logout,
            height=45,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=self.colors['danger'],
            hover_color="#c82333",
            corner_radius=10
        )
        logout_btn.pack(side="bottom", fill="x", padx=20, pady=20)
    
    def create_content_area(self, parent):
        """Create the main content area"""
        content_container = ctk.CTkFrame(parent, corner_radius=0, fg_color=self.colors['light'])
        content_container.pack(side="right", fill="both", expand=True)

        # Top header bar
        self.create_top_header(content_container)

        # Main content with tabs
        self.create_tabbed_content(content_container)

    def create_top_header(self, parent):
        """Create the top header bar"""
        header = ctk.CTkFrame(
            parent,
            height=80,
            corner_radius=0,
            fg_color=self.colors['white']
        )
        header.pack(fill="x", padx=0, pady=0)
        header.pack_propagate(False)

        # Header content
        header_content = ctk.CTkFrame(header, fg_color="transparent")
        header_content.pack(fill="both", expand=True, padx=30, pady=20)

        # Welcome message
        welcome_label = ctk.CTkLabel(
            header_content,
            text=f"{self.app.language_service.translate('dashboard.welcome')}, {self.app.current_user.first_name}! 👋",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.colors['dark']
        )
        welcome_label.pack(side="left")

        # Right side controls
        controls_frame = ctk.CTkFrame(header_content, fg_color="transparent")
        controls_frame.pack(side="right")

        # Language selector
        self.language_var = ctk.StringVar(value=self.app.language_service.current_language)
        self.language_combo = ctk.CTkComboBox(
            controls_frame,
            variable=self.language_var,
            values=["en", "ar"],
            state="readonly",
            width=100,
            height=35,
            command=self.on_language_change
        )
        self.language_combo.pack(side="right", padx=(10, 0))

        lang_label = ctk.CTkLabel(
            controls_frame,
            text=f"🌐 {self.app.language_service.translate('login.language')}:",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['dark']
        )
        lang_label.pack(side="right", padx=(0, 5))

    def create_tabbed_content(self, parent):
        """Create tabbed content area"""
        # Create tabview
        tabview = ctk.CTkTabview(parent, height=400)
        tabview.pack(fill="both", expand=True, padx=20, pady=(10, 20))

        # Dashboard tab
        dashboard_tab = tabview.add(self.app.language_service.translate("dashboard.title"))
        self.create_dashboard_content(dashboard_tab)

        # Analytics tab
        analytics_tab = tabview.add("📊 Analytics")
        self.create_analytics_content(analytics_tab)

        # Quick Actions tab
        actions_tab = tabview.add(self.app.language_service.translate("dashboard.quick_actions"))
        self.create_quick_actions_content(actions_tab)
    
    def create_dashboard_content(self, parent):
        """Create the main dashboard content"""
        # Stats cards row
        self.create_stats_cards(parent)

        # Content grid
        content_grid = ctk.CTkFrame(parent, fg_color="transparent")
        content_grid.pack(fill="both", expand=True, pady=20)

        # Left column - Recent activities
        left_column = ctk.CTkFrame(content_grid, fg_color="transparent")
        left_column.pack(side="left", fill="both", expand=True, padx=(0, 10))

        self.create_recent_activities(left_column)

        # Right column - Quick stats
        right_column = ctk.CTkFrame(content_grid, fg_color="transparent")
        right_column.pack(side="right", fill="y", padx=(10, 0))

        self.create_quick_stats(right_column)

    def create_stats_cards(self, parent):
        """Create professional statistics cards"""
        cards_frame = ctk.CTkFrame(parent, fg_color="transparent")
        cards_frame.pack(fill="x", pady=(0, 20))

        cards_data = [
            {
                "title": self.app.language_service.translate("dashboard.customers"),
                "value": "1,234",
                "icon": "👥",
                "change": "+12%",
                "color": self.colors['primary']
            },
            {
                "title": self.app.language_service.translate("dashboard.products"),
                "value": "567",
                "icon": "📦",
                "change": "+5%",
                "color": self.colors['success']
            },
            {
                "title": self.app.language_service.translate("dashboard.sales"),
                "value": "$12,345",
                "icon": "💰",
                "change": "+18%",
                "color": self.colors['warning']
            },
            {
                "title": self.app.language_service.translate("dashboard.orders"),
                "value": "89",
                "icon": "📋",
                "change": "-3%",
                "color": self.colors['danger']
            }
        ]

        for i, card_data in enumerate(cards_data):
            card = ctk.CTkFrame(
                cards_frame,
                height=120,
                corner_radius=15,
                fg_color=self.colors['white'],
                border_width=1,
                border_color=self.colors['light']
            )
            card.pack(side="left", fill="x", expand=True, padx=(0, 15 if i < len(cards_data)-1 else 0))
            card.pack_propagate(False)

            # Card content
            card_content = ctk.CTkFrame(card, fg_color="transparent")
            card_content.pack(fill="both", expand=True, padx=20, pady=15)

            # Top row - icon and change
            top_row = ctk.CTkFrame(card_content, fg_color="transparent")
            top_row.pack(fill="x")

            # Icon
            icon_label = ctk.CTkLabel(
                top_row,
                text=card_data["icon"],
                font=ctk.CTkFont(size=24)
            )
            icon_label.pack(side="left")

            # Change indicator
            change_label = ctk.CTkLabel(
                top_row,
                text=card_data["change"],
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.colors['success'] if '+' in card_data["change"] else self.colors['danger']
            )
            change_label.pack(side="right")

            # Value
            value_label = ctk.CTkLabel(
                card_content,
                text=card_data["value"],
                font=ctk.CTkFont(size=28, weight="bold"),
                text_color=card_data["color"]
            )
            value_label.pack(pady=(10, 5))

            # Title
            title_label = ctk.CTkLabel(
                card_content,
                text=card_data["title"],
                font=ctk.CTkFont(size=12),
                text_color=self.colors['dark']
            )
            title_label.pack()

    def create_recent_activities(self, parent):
        """Create recent activities section"""
        activities_frame = ctk.CTkFrame(
            parent,
            corner_radius=15,
            fg_color=self.colors['white'],
            border_width=1,
            border_color=self.colors['light']
        )
        activities_frame.pack(fill="both", expand=True)

        # Header
        header = ctk.CTkLabel(
            activities_frame,
            text=f"📋 {self.app.language_service.translate('dashboard.recent_activities')}",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['dark']
        )
        header.pack(pady=(20, 15), padx=20, anchor="w")

        # Activities list
        activities_list = ctk.CTkScrollableFrame(
            activities_frame,
            fg_color="transparent"
        )
        activities_list.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Sample activities
        activities = [
            {"icon": "👤", "text": "New customer registered: ABC Company", "time": "2 minutes ago", "color": self.colors['success']},
            {"icon": "💰", "text": "Sale order #1234 created for $5,000", "time": "15 minutes ago", "color": self.colors['primary']},
            {"icon": "💳", "text": "Payment received from XYZ Corp: $2,500", "time": "1 hour ago", "color": self.colors['success']},
            {"icon": "⚠️", "text": "Low stock alert: Product ABC-123", "time": "2 hours ago", "color": self.colors['warning']},
            {"icon": "🏪", "text": "New supplier added: Tech Solutions Ltd", "time": "3 hours ago", "color": self.colors['primary']},
        ]

        for activity in activities:
            activity_item = ctk.CTkFrame(
                activities_list,
                height=60,
                corner_radius=10,
                fg_color=self.colors['light']
            )
            activity_item.pack(fill="x", pady=5)
            activity_item.pack_propagate(False)

            # Activity content
            content = ctk.CTkFrame(activity_item, fg_color="transparent")
            content.pack(fill="both", expand=True, padx=15, pady=10)

            # Icon
            icon_label = ctk.CTkLabel(
                content,
                text=activity["icon"],
                font=ctk.CTkFont(size=20)
            )
            icon_label.pack(side="left", padx=(0, 15))

            # Text and time
            text_frame = ctk.CTkFrame(content, fg_color="transparent")
            text_frame.pack(side="left", fill="both", expand=True)

            text_label = ctk.CTkLabel(
                text_frame,
                text=activity["text"],
                font=ctk.CTkFont(size=12),
                text_color=self.colors['dark'],
                anchor="w"
            )
            text_label.pack(anchor="w")

            time_label = ctk.CTkLabel(
                text_frame,
                text=activity["time"],
                font=ctk.CTkFont(size=10),
                text_color=activity["color"],
                anchor="w"
            )
            time_label.pack(anchor="w")

    def create_quick_stats(self, parent):
        """Create quick stats section"""
        stats_frame = ctk.CTkFrame(
            parent,
            width=300,
            corner_radius=15,
            fg_color=self.colors['white'],
            border_width=1,
            border_color=self.colors['light']
        )
        stats_frame.pack(fill="y")
        stats_frame.pack_propagate(False)

        # Header
        header = ctk.CTkLabel(
            stats_frame,
            text="📊 Quick Stats",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['dark']
        )
        header.pack(pady=(20, 15), padx=20, anchor="w")

        # Stats items
        stats_items = [
            {"label": "Today's Sales", "value": "$2,450", "icon": "💰"},
            {"label": "Pending Orders", "value": "23", "icon": "📋"},
            {"label": "Low Stock Items", "value": "5", "icon": "⚠️"},
            {"label": "New Customers", "value": "12", "icon": "👥"},
        ]

        for item in stats_items:
            stat_item = ctk.CTkFrame(
                stats_frame,
                height=50,
                corner_radius=10,
                fg_color=self.colors['light']
            )
            stat_item.pack(fill="x", padx=20, pady=5)
            stat_item.pack_propagate(False)

            # Content
            content = ctk.CTkFrame(stat_item, fg_color="transparent")
            content.pack(fill="both", expand=True, padx=15, pady=10)

            # Icon and label
            icon_label = ctk.CTkLabel(
                content,
                text=item["icon"],
                font=ctk.CTkFont(size=16)
            )
            icon_label.pack(side="left")

            label = ctk.CTkLabel(
                content,
                text=item["label"],
                font=ctk.CTkFont(size=12),
                text_color=self.colors['dark']
            )
            label.pack(side="left", padx=(10, 0))

            # Value
            value = ctk.CTkLabel(
                content,
                text=item["value"],
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.colors['primary']
            )
            value.pack(side="right")

    def create_analytics_content(self, parent):
        """Create analytics content with charts"""
        # Chart placeholder
        chart_frame = ctk.CTkFrame(
            parent,
            corner_radius=15,
            fg_color=self.colors['white']
        )
        chart_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Chart title
        title = ctk.CTkLabel(
            chart_frame,
            text="📈 Sales Analytics",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=self.colors['dark']
        )
        title.pack(pady=(20, 10))

        # Chart placeholder (you can integrate matplotlib here)
        chart_placeholder = ctk.CTkLabel(
            chart_frame,
            text="📊\n\nSales Chart\n(Chart visualization will be displayed here)\n\nIntegrate with matplotlib for real charts",
            font=ctk.CTkFont(size=16),
            text_color=self.colors['dark']
        )
        chart_placeholder.pack(expand=True, pady=50)

    def create_quick_actions_content(self, parent):
        """Create quick actions content"""
        actions_frame = ctk.CTkFrame(parent, fg_color="transparent")
        actions_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            actions_frame,
            text="⚡ Quick Actions",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=self.colors['dark']
        )
        title.pack(pady=(0, 20))

        # Actions grid
        actions_grid = ctk.CTkFrame(actions_frame, fg_color="transparent")
        actions_grid.pack(fill="both", expand=True)

        actions = [
            ("👥", "New Customer", self.placeholder_command, self.colors['primary']),
            ("📦", "New Product", self.placeholder_command, self.colors['success']),
            ("💰", "New Sale", self.placeholder_command, self.colors['warning']),
            ("🛒", "New Purchase", self.placeholder_command, self.colors['secondary']),
            ("🧾", "Create Invoice", self.placeholder_command, self.colors['primary']),
            ("📊", "View Reports", self.placeholder_command, self.colors['success']),
            ("💾", "Backup Database", self.backup_database, self.colors['danger']),
            ("⚙️", "Settings", self.placeholder_command, self.colors['dark']),
        ]

        for i, (icon, text, command, color) in enumerate(actions):
            row = i // 4
            col = i % 4

            action_btn = ctk.CTkButton(
                actions_grid,
                text=f"{icon}\n{text}",
                command=command,
                width=200,
                height=100,
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=color,
                hover_color=self.darken_color(color),
                corner_radius=15
            )
            action_btn.grid(row=row, column=col, padx=10, pady=10, sticky="ew")

        # Configure grid weights
        for i in range(4):
            actions_grid.columnconfigure(i, weight=1)

    def darken_color(self, color):
        """Darken a color for hover effect"""
        # Simple color darkening - in production, use proper color manipulation
        color_map = {
            self.colors['primary']: '#1e5f7a',
            self.colors['success']: '#1e7e34',
            self.colors['warning']: '#d39e00',
            self.colors['secondary']: '#7a2d5a',
            self.colors['danger']: '#bd2130',
            self.colors['dark']: '#23272b'
        }
        return color_map.get(color, color)

    def show_dashboard(self):
        """Show dashboard (already showing)"""
        pass

    def on_language_change(self, choice=None):
        """Handle language change"""
        # Get the selected language from the combobox
        new_language = choice if choice else self.language_var.get()

        # Update language service
        if self.app.language_service.set_language(new_language):
            self.show_language_change_dialog()

    def show_language_change_dialog(self):
        """Show language change confirmation dialog"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Language Changed" if self.app.language_service.current_language == 'en' else "تم تغيير اللغة")
        dialog.geometry("350x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 175
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 100
        dialog.geometry(f"350x200+{x}+{y}")

        # Content
        icon_label = ctk.CTkLabel(
            dialog,
            text="🌐",
            font=ctk.CTkFont(size=40)
        )
        icon_label.pack(pady=(30, 10))

        if self.app.language_service.current_language == 'en':
            message_text = "Language changed to English successfully!\nSome elements will update immediately."
            button_text = "OK"
        else:
            message_text = "تم تغيير اللغة إلى العربية بنجاح!\nسيتم تحديث بعض العناصر فوراً."
            button_text = "موافق"

        message_label = ctk.CTkLabel(
            dialog,
            text=message_text,
            font=ctk.CTkFont(size=14),
            wraplength=300
        )
        message_label.pack(pady=(0, 20))

        ok_button = ctk.CTkButton(
            dialog,
            text=button_text,
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color=self.colors['primary']
        )
        ok_button.pack(pady=(0, 20))

        # Auto close after 3 seconds
        dialog.after(3000, dialog.destroy)

    def placeholder_command(self):
        """Placeholder command for menu items"""
        # Create custom dialog
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Coming Soon")
        dialog.geometry("400x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 200
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 100
        dialog.geometry(f"400x200+{x}+{y}")

        # Content
        icon_label = ctk.CTkLabel(
            dialog,
            text="🚧",
            font=ctk.CTkFont(size=40)
        )
        icon_label.pack(pady=(30, 10))

        message_label = ctk.CTkLabel(
            dialog,
            text="This feature will be implemented in the next version.",
            font=ctk.CTkFont(size=14),
            wraplength=350
        )
        message_label.pack(pady=(0, 20))

        ok_button = ctk.CTkButton(
            dialog,
            text="OK",
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color=self.colors['primary']
        )
        ok_button.pack(pady=(0, 20))

    def backup_database(self):
        """Backup database with professional dialog"""
        try:
            backup_path = self.app.db_service.backup_database()

            # Success dialog
            dialog = ctk.CTkToplevel(self.root)
            dialog.title("Backup Complete")
            dialog.geometry("450x250")
            dialog.resizable(False, False)
            dialog.transient(self.root)
            dialog.grab_set()

            # Center dialog
            dialog.update_idletasks()
            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 225
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 125
            dialog.geometry(f"450x250+{x}+{y}")

            # Content
            icon_label = ctk.CTkLabel(
                dialog,
                text="✅",
                font=ctk.CTkFont(size=40)
            )
            icon_label.pack(pady=(30, 10))

            message_label = ctk.CTkLabel(
                dialog,
                text="Database backup created successfully!",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=self.colors['success']
            )
            message_label.pack(pady=(0, 10))

            path_label = ctk.CTkLabel(
                dialog,
                text=f"Backup saved to:\n{backup_path}",
                font=ctk.CTkFont(size=12),
                wraplength=400
            )
            path_label.pack(pady=(0, 20))

            ok_button = ctk.CTkButton(
                dialog,
                text="OK",
                command=dialog.destroy,
                width=100,
                height=35,
                fg_color=self.colors['success']
            )
            ok_button.pack(pady=(0, 20))

        except Exception as e:
            # Error dialog
            dialog = ctk.CTkToplevel(self.root)
            dialog.title("Backup Failed")
            dialog.geometry("400x200")
            dialog.resizable(False, False)
            dialog.transient(self.root)
            dialog.grab_set()

            # Center dialog
            dialog.update_idletasks()
            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 200
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 100
            dialog.geometry(f"400x200+{x}+{y}")

            # Content
            icon_label = ctk.CTkLabel(
                dialog,
                text="❌",
                font=ctk.CTkFont(size=40)
            )
            icon_label.pack(pady=(30, 10))

            message_label = ctk.CTkLabel(
                dialog,
                text=f"Failed to create backup:\n{str(e)}",
                font=ctk.CTkFont(size=14),
                wraplength=350
            )
            message_label.pack(pady=(0, 20))

            ok_button = ctk.CTkButton(
                dialog,
                text="OK",
                command=dialog.destroy,
                width=100,
                height=35,
                fg_color=self.colors['danger']
            )
            ok_button.pack(pady=(0, 20))

    def show_about(self):
        """Show about dialog with professional styling"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("About Management System")
        dialog.geometry("500x400")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 250
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 200
        dialog.geometry(f"500x400+{x}+{y}")

        # Header
        header_frame = ctk.CTkFrame(
            dialog,
            height=100,
            corner_radius=0,
            fg_color=self.colors['primary']
        )
        header_frame.pack(fill="x")
        header_frame.pack_propagate(False)

        # Logo
        logo_label = ctk.CTkLabel(
            header_frame,
            text="🏢",
            font=ctk.CTkFont(size=40),
            text_color=self.colors['white']
        )
        logo_label.pack(pady=(20, 5))

        title_label = ctk.CTkLabel(
            header_frame,
            text=f"{self.app.language_service.translate('app.title')} {self.app.language_service.translate('app.version')}",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['white']
        )
        title_label.pack()

        # Content
        content_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, padx=30, pady=30)

        about_text = """A comprehensive business management system built with Python and SQLite3.

Features:
• Multi-language support (Arabic/English)
• Customer and supplier management
• Inventory and sales tracking
• Financial management
• Comprehensive reporting
• Professional user interface

© 2024 Management System Development Team"""

        text_label = ctk.CTkLabel(
            content_frame,
            text=about_text,
            font=ctk.CTkFont(size=12),
            justify="left",
            anchor="w"
        )
        text_label.pack(fill="both", expand=True)

        # Close button
        close_button = ctk.CTkButton(
            content_frame,
            text="Close",
            command=dialog.destroy,
            width=100,
            height=35,
            fg_color=self.colors['primary']
        )
        close_button.pack(pady=(20, 0))

    def logout(self):
        """Logout with confirmation dialog"""
        # Confirmation dialog
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Logout Confirmation")
        dialog.geometry("350x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 175
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 100
        dialog.geometry(f"350x200+{x}+{y}")

        # Content
        icon_label = ctk.CTkLabel(
            dialog,
            text="🚪",
            font=ctk.CTkFont(size=40)
        )
        icon_label.pack(pady=(30, 10))

        message_label = ctk.CTkLabel(
            dialog,
            text="Are you sure you want to logout?",
            font=ctk.CTkFont(size=14),
            wraplength=300
        )
        message_label.pack(pady=(0, 20))

        # Buttons
        button_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        button_frame.pack(pady=(0, 20))

        def confirm_logout():
            dialog.destroy()
            self.root.destroy()
            self.app.logout()

        yes_button = ctk.CTkButton(
            button_frame,
            text="Yes",
            command=confirm_logout,
            width=80,
            height=35,
            fg_color=self.colors['danger']
        )
        yes_button.pack(side="left", padx=(0, 10))

        no_button = ctk.CTkButton(
            button_frame,
            text="No",
            command=dialog.destroy,
            width=80,
            height=35,
            fg_color=self.colors['primary']
        )
        no_button.pack(side="left")

    def on_window_close(self):
        """Handle window close event"""
        # Confirmation dialog
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Exit Confirmation")
        dialog.geometry("350x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 175
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 100
        dialog.geometry(f"350x200+{x}+{y}")

        # Content
        icon_label = ctk.CTkLabel(
            dialog,
            text="❓",
            font=ctk.CTkFont(size=40)
        )
        icon_label.pack(pady=(30, 10))

        message_label = ctk.CTkLabel(
            dialog,
            text="Are you sure you want to exit the application?",
            font=ctk.CTkFont(size=14),
            wraplength=300
        )
        message_label.pack(pady=(0, 20))

        # Buttons
        button_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        button_frame.pack(pady=(0, 20))

        def confirm_exit():
            dialog.destroy()
            self.root.destroy()
            self.app.cleanup()

        yes_button = ctk.CTkButton(
            button_frame,
            text="Yes",
            command=confirm_exit,
            width=80,
            height=35,
            fg_color=self.colors['danger']
        )
        yes_button.pack(side="left", padx=(0, 10))

        no_button = ctk.CTkButton(
            button_frame,
            text="No",
            command=dialog.destroy,
            width=80,
            height=35,
            fg_color=self.colors['primary']
        )
        no_button.pack(side="left")

    def show(self):
        """Show the dashboard window"""
        self.root.mainloop()
    
    # This method is now handled by the new create_dashboard_content method above
    # Removed old ttk-based implementation
    
    # This method is now handled by the new create_quick_actions_content method above
    # Removed old ttk-based implementation
    
    def on_language_change(self, event=None):
        """Handle language change"""
        # For now, show a message that restart is required
        messagebox.showinfo(
            "Language Change",
            "Please restart the application to apply the language change."
        )
    
    def placeholder_command(self):
        """Placeholder command for menu items"""
        messagebox.showinfo("Coming Soon", "This feature will be implemented in the next version.")
    
    def backup_database(self):
        """Backup database"""
        try:
            backup_path = self.app.db_service.backup_database()
            messagebox.showinfo("Backup Complete", f"Database backup created successfully:\n{backup_path}")
        except Exception as e:
            messagebox.showerror("Backup Failed", f"Failed to create backup:\n{str(e)}")
    
    def show_about(self):
        """Show about dialog"""
        about_text = f"""
{self.app.language_service.translate('app.title')} {self.app.language_service.translate('app.version')}

A comprehensive business management system built with Python and SQLite3.

Features:
• Multi-language support (Arabic/English)
• Customer and supplier management
• Inventory and sales tracking
• Financial management
• Comprehensive reporting

© 2024 Management System Development Team
        """
        messagebox.showinfo("About", about_text.strip())
    
    def logout(self):
        """Logout current user"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.root.destroy()
            self.app.logout()
    
    def on_window_close(self):
        """Handle window close event"""
        if messagebox.askyesno("Exit", "Are you sure you want to exit the application?"):
            self.root.destroy()
            self.app.cleanup()
    
    def show(self):
        """Show the dashboard window"""
        self.root.mainloop()
