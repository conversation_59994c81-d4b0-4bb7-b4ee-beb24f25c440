"""
Dashboard view for the Management System
"""
import tkinter as tk
from tkinter import ttk, messagebox
import logging

logger = logging.getLogger(__name__)

class DashboardView:
    """Main dashboard window"""
    
    def __init__(self, app):
        self.app = app
        self.root = None
        self.create_dashboard()
    
    def create_dashboard(self):
        """Create the main dashboard window"""
        self.root = tk.Tk()
        self.root.title(f"{self.app.language_service.translate('app.title')} - {self.app.language_service.translate('dashboard.title')}")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # Maximize window on Windows
        
        # Configure style
        self.configure_style()
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create main layout
        self.create_main_layout()
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
    
    def configure_style(self):
        """Configure TTK styles"""
        style = ttk.Style()
        
        # Configure styles for better appearance
        style.configure("Title.TLabel", font=("Segoe UI", 16, "bold"))
        style.configure("Heading.TLabel", font=("Segoe UI", 12, "bold"))
        style.configure("Card.TFrame", relief="raised", borderwidth=1)
    
    def create_menu_bar(self):
        """Create the menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New", command=self.placeholder_command)
        file_menu.add_command(label="Open", command=self.placeholder_command)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_window_close)
        
        # Modules menu
        modules_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label=self.app.language_service.translate("menu.customers"), menu=modules_menu)
        modules_menu.add_command(label=self.app.language_service.translate("menu.customers"), command=self.placeholder_command)
        modules_menu.add_command(label=self.app.language_service.translate("menu.inventory"), command=self.placeholder_command)
        modules_menu.add_command(label=self.app.language_service.translate("menu.sales"), command=self.placeholder_command)
        modules_menu.add_command(label=self.app.language_service.translate("menu.purchases"), command=self.placeholder_command)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Backup Database", command=self.backup_database)
        tools_menu.add_command(label="Settings", command=self.placeholder_command)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        
        # User menu (right side)
        user_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label=f"👤 {self.app.current_user.first_name}", menu=user_menu)
        user_menu.add_command(label="Profile", command=self.placeholder_command)
        user_menu.add_command(label="Change Password", command=self.placeholder_command)
        user_menu.add_separator()
        user_menu.add_command(label=self.app.language_service.translate("menu.logout"), command=self.logout)
    
    def create_main_layout(self):
        """Create the main dashboard layout"""
        # Main container
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Header
        header_frame = ttk.Frame(main_container)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        welcome_label = ttk.Label(
            header_frame,
            text=f"{self.app.language_service.translate('dashboard.welcome')}, {self.app.current_user.first_name}!",
            style="Title.TLabel"
        )
        welcome_label.pack(side=tk.LEFT)
        
        # Language selector
        lang_frame = ttk.Frame(header_frame)
        lang_frame.pack(side=tk.RIGHT)
        
        ttk.Label(lang_frame, text=self.app.language_service.translate("login.language")).pack(side=tk.LEFT, padx=(0, 5))
        
        language_var = tk.StringVar(value=self.app.language_service.current_language)
        language_combo = ttk.Combobox(
            lang_frame,
            textvariable=language_var,
            values=["en", "ar"],
            state="readonly",
            width=8
        )
        language_combo.pack(side=tk.LEFT)
        language_combo.bind("<<ComboboxSelected>>", self.on_language_change)
        
        # Stats cards row
        stats_frame = ttk.Frame(main_container)
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.create_stats_cards(stats_frame)
        
        # Content area with notebook
        content_frame = ttk.Frame(main_container)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        notebook = ttk.Notebook(content_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Dashboard tab
        dashboard_tab = ttk.Frame(notebook)
        notebook.add(dashboard_tab, text=self.app.language_service.translate("dashboard.title"))
        
        self.create_dashboard_content(dashboard_tab)
        
        # Quick actions tab
        actions_tab = ttk.Frame(notebook)
        notebook.add(actions_tab, text=self.app.language_service.translate("dashboard.quick_actions"))
        
        self.create_quick_actions(actions_tab)
    
    def create_stats_cards(self, parent):
        """Create statistics cards"""
        cards_data = [
            {"title": self.app.language_service.translate("dashboard.customers"), "value": "1,234", "icon": "👥"},
            {"title": self.app.language_service.translate("dashboard.products"), "value": "567", "icon": "📦"},
            {"title": self.app.language_service.translate("dashboard.sales"), "value": "$12,345", "icon": "💰"},
            {"title": self.app.language_service.translate("dashboard.orders"), "value": "89", "icon": "📋"}
        ]
        
        for i, card_data in enumerate(cards_data):
            card_frame = ttk.Frame(parent, style="Card.TFrame")
            card_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10 if i < len(cards_data)-1 else 0))
            
            # Icon and title
            header_frame = ttk.Frame(card_frame)
            header_frame.pack(fill=tk.X, padx=15, pady=(15, 5))
            
            ttk.Label(header_frame, text=card_data["icon"], font=("Segoe UI", 20)).pack(side=tk.LEFT)
            ttk.Label(header_frame, text=card_data["title"], style="Heading.TLabel").pack(side=tk.RIGHT)
            
            # Value
            ttk.Label(
                card_frame,
                text=card_data["value"],
                font=("Segoe UI", 24, "bold"),
                foreground="blue"
            ).pack(padx=15, pady=(0, 15))
    
    def create_dashboard_content(self, parent):
        """Create dashboard content"""
        # Split into left and right panels
        left_frame = ttk.Frame(parent)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        right_frame = ttk.Frame(parent)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        
        # Recent activities (left panel)
        activities_frame = ttk.LabelFrame(left_frame, text=self.app.language_service.translate("dashboard.recent_activities"))
        activities_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        activities_text = tk.Text(activities_frame, height=10, wrap=tk.WORD)
        activities_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Sample activities
        sample_activities = [
            "• New customer registered: ABC Company",
            "• Sale order #1234 created for $5,000",
            "• Payment received from XYZ Corp: $2,500",
            "• Low stock alert: Product ABC-123",
            "• New supplier added: Tech Solutions Ltd"
        ]
        
        activities_text.insert(tk.END, "\n".join(sample_activities))
        activities_text.config(state=tk.DISABLED)
        
        # Chart placeholder (left panel)
        chart_frame = ttk.LabelFrame(left_frame, text="Sales Chart")
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        chart_label = ttk.Label(chart_frame, text="📊 Sales Chart\n(Chart will be displayed here)", 
                               font=("Segoe UI", 12), anchor="center")
        chart_label.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Quick actions (right panel)
        actions_frame = ttk.LabelFrame(right_frame, text=self.app.language_service.translate("dashboard.quick_actions"))
        actions_frame.pack(fill=tk.BOTH, expand=True)
        
        quick_actions = [
            ("+ New Customer", self.placeholder_command),
            ("+ New Product", self.placeholder_command),
            ("+ New Sale", self.placeholder_command),
            ("+ New Purchase", self.placeholder_command),
            ("📊 View Reports", self.placeholder_command),
            ("⚙️ Settings", self.placeholder_command)
        ]
        
        for action_text, command in quick_actions:
            btn = ttk.Button(actions_frame, text=action_text, command=command)
            btn.pack(fill=tk.X, padx=10, pady=5)
    
    def create_quick_actions(self, parent):
        """Create quick actions tab content"""
        ttk.Label(parent, text="Quick Actions", style="Title.TLabel").pack(pady=20)
        
        # Create a grid of action buttons
        actions_grid = ttk.Frame(parent)
        actions_grid.pack(fill=tk.BOTH, expand=True, padx=20)
        
        actions = [
            ("👥 Customer Management", self.placeholder_command),
            ("📦 Inventory Management", self.placeholder_command),
            ("💰 Sales Management", self.placeholder_command),
            ("🛒 Purchase Management", self.placeholder_command),
            ("🏪 Supplier Management", self.placeholder_command),
            ("👨‍💼 Employee Management", self.placeholder_command),
            ("💳 Finance Management", self.placeholder_command),
            ("🧾 Invoice Management", self.placeholder_command),
            ("📊 Reports", self.placeholder_command),
            ("📁 Documents", self.placeholder_command),
            ("⚙️ Settings", self.placeholder_command),
            ("🔧 Database Tools", self.backup_database)
        ]
        
        for i, (action_text, command) in enumerate(actions):
            row = i // 3
            col = i % 3
            
            btn = ttk.Button(actions_grid, text=action_text, command=command)
            btn.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # Configure grid weights
        for i in range(3):
            actions_grid.columnconfigure(i, weight=1)
    
    def on_language_change(self, event=None):
        """Handle language change"""
        # For now, show a message that restart is required
        messagebox.showinfo(
            "Language Change",
            "Please restart the application to apply the language change."
        )
    
    def placeholder_command(self):
        """Placeholder command for menu items"""
        messagebox.showinfo("Coming Soon", "This feature will be implemented in the next version.")
    
    def backup_database(self):
        """Backup database"""
        try:
            backup_path = self.app.db_service.backup_database()
            messagebox.showinfo("Backup Complete", f"Database backup created successfully:\n{backup_path}")
        except Exception as e:
            messagebox.showerror("Backup Failed", f"Failed to create backup:\n{str(e)}")
    
    def show_about(self):
        """Show about dialog"""
        about_text = f"""
{self.app.language_service.translate('app.title')} {self.app.language_service.translate('app.version')}

A comprehensive business management system built with Python and SQLite3.

Features:
• Multi-language support (Arabic/English)
• Customer and supplier management
• Inventory and sales tracking
• Financial management
• Comprehensive reporting

© 2024 Management System Development Team
        """
        messagebox.showinfo("About", about_text.strip())
    
    def logout(self):
        """Logout current user"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.root.destroy()
            self.app.logout()
    
    def on_window_close(self):
        """Handle window close event"""
        if messagebox.askyesno("Exit", "Are you sure you want to exit the application?"):
            self.root.destroy()
            self.app.cleanup()
    
    def show(self):
        """Show the dashboard window"""
        self.root.mainloop()
